# Arkime Session JSON 适配逻辑修复

## 问题分析

### 1. 重复适配问题
**问题描述**：在 `dpi_arkime_session.c` 中，代码先调用 `record_write_json(record, session)` 将原始record写入JSON，然后又进行适配处理，导致重复处理。

**修复方案**：移除了重复的 `record_write_json` 调用，只使用适配后的record构建JSON。

### 2. 适配脚本逻辑错误
**问题描述**：`adapt_general.lua` 中的字段映射不正确，特别是时间字段。

**修复内容**：
```lua
-- 修复前
{from_name = "begTime", to_name = "begTime", tll = 1},
{from_name = "endTime", to_name = "endTime", tll = 1},
{from_name = "comDur", to_name = "comDur", tll = 1},

-- 修复后
{from_name = "begTime", to_name = "firstPacket", tll = 1},
{from_name = "endTime", to_name = "lastPacket", tll = 1},
{from_name = "comDur", to_name = "length", tll = 1},
```

### 3. JSON格式不符合Arkime标准
**问题描述**：原来的JSON输出格式缺少协议层的嵌套结构。

**修复方案**：重新设计JSON构建逻辑，支持协议层的嵌套结构。

## 新的JSON格式

修复后的JSON格式符合以下结构：

```json
{
  // general字段（第一级）
  "@timestamp": 1752149929057,     // 毫秒时间戳，处理时间
  "firstPacket": 1041342931300,    // session第一包时间戳
  "lastPacket": 1041342932300,     // session最后一包时间戳
  "length": 1000,                  // 会话持续时间(毫秒)
  
  // 网络信息
  "source": {
    "ip": "***********",
    "port": 80,
    "bytes": 1024,
    "packets": 10
  },
  "destination": {
    "ip": "***********", 
    "port": 8080,
    "bytes": 2048,
    "packets": 15
  },
  "network": {
    "bytes": 3072,
    "packets": 25
  },
  
  // 协议信息
  "protocols": [
    "udp"  // 链路层协议
  ],
  
  // 应用层协议（嵌套对象）
  "http": {
    "method": "PUT"  // http layer 中的字段
  },
  
  // 文件位置信息
  "packetPos": [2162688],
  "fileId": [1],
  
  // 节点信息
  "node": "arkime-node"
}
```

## 代码修改详情

### 1. dpi_arkime_session.c 主要修改

1. **移除重复适配**：删除了 `record_write_json(record, session)` 调用
2. **重新设计JSON构建逻辑**：
   - 使用适配引擎处理record
   - general层字段添加到JSON第一级
   - 其他协议层作为嵌套对象
   - 构建protocols数组

3. **支持数字类型字段**：对时间和长度字段进行数字类型转换

### 2. adapt_general.lua 修改

修正了时间字段的映射关系，使其符合Arkime标准：
- `begTime` → `firstPacket`
- `endTime` → `lastPacket`  
- `comDur` → `length`

## 测试验证

创建了测试程序 `test_session_json.c` 来验证JSON格式的正确性：

```bash
cd sdx_dpi_sdt/src/arkime
make -f Makefile.test test
```

测试程序会验证：
1. 必需字段的存在性
2. protocols数组的正确性
3. 协议层对象的嵌套结构
4. source/destination对象的完整性

## 使用说明

1. **编译**：确保系统已安装cJSON库
2. **配置**：检查ES配置是否正确启用
3. **运行**：系统会自动使用新的JSON格式发送到ES

## 注意事项

1. **向后兼容性**：新格式可能与旧版本的ES索引不兼容，建议创建新的索引
2. **性能影响**：适配处理会增加一定的CPU开销
3. **错误处理**：适配失败时会回退到原始record处理

## 相关文件

- `sdx_dpi_sdt/src/arkime/dpi_arkime_session.c` - 主要修改文件
- `sdx_dpi_sdt/run/adapt_lua/adapt_general.lua` - 适配脚本修改
- `sdx_dpi_sdt/src/arkime/test_session_json.c` - 测试程序
- `sdx_dpi_sdt/src/arkime/Makefile.test` - 测试编译文件
