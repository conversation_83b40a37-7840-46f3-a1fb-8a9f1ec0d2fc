# Session构造架构优化

## 核心改进

**问题**: 原始代码在C中使用大量if-else判断处理general层字段映射
**解决**: 将字段映射逻辑移至Lua适配层，C代码专注JSON构造

## 新架构流程

```
common字段 -> [Lua适配层] -> general字段 -> [C代码] -> session JSON
```

### 1. <PERSON><PERSON>适配层 (adapt_general.lua)
负责字段映射和语义转换：
```lua
{from_name = "srcAddr", to_name = "srcAddr", tll = 1},     -- 地址映射
{from_name = "begTime", to_name = "firstPacket", tll = 1}, -- 时间语义转换
{from_name = "upBytes", to_name = "upBytes", tll = 1},     -- 流量统计
```

### 2. C代码 (dpi_arkime_session.c)
统一处理所有字段 + 后处理构造必须字段：

```c
// 统一字段处理
cJSON *proto_obj = cJSON_GetObjectItem(session, layer_name);
if (!proto_obj) {
    proto_obj = cJSON_CreateObject();
    cJSON_AddItemToObject(session, layer_name, proto_obj);
}

// 智能类型转换
char *endptr;
long long num_val = strtoll(value_str, &endptr, 10);
if (*endptr == '\0') {
    cJSON_AddNumberToObject(proto_obj, field_name, num_val);
} else {
    cJSON_AddStringToObject(proto_obj, field_name, value_str);
}

// 后处理：从general对象构造Arkime必须字段
cJSON *general_obj = cJSON_GetObjectItem(session, "general");
if (general_obj) {
    cJSON *src_addr = cJSON_GetObjectItem(general_obj, "srcAddr");
    if (src_addr) {
        cJSON_AddStringToObject(source, "ip", cJSON_GetStringValue(src_addr));
    }
    // ... 其他字段提取
}
```

## 架构优势

### 代码简化
- **旧方案**: 60+ 行if-else判断
- **新方案**: 10+ 行统一处理 + 后处理逻辑

### 维护性提升
- 字段映射规则集中在Lua文件
- 新增字段只需修改adapt_general.lua
- C代码逻辑清晰，专注JSON构造

### 灵活性增强
- 可通过Lua文件动态调整映射规则
- 易于扩展新协议适配
- 支持复杂的字段转换逻辑

## 字段映射关系

### Lua适配层映射
```
common.srcAddr -> general.srcAddr
common.begTime -> general.firstPacket
common.upBytes -> general.upBytes
```

### C代码后处理映射
```
general.srcAddr -> source.ip
general.dstAddr -> destination.ip
general.srcPort -> source.port
general.dstPort -> destination.port
general.upBytes -> source.bytes
general.downBytes -> destination.bytes
general.firstPacket -> session.firstPacket
general.lastPacket -> session.lastPacket
general.length -> session.length
general.proto -> session.ipProtocol
```

## 最终JSON结构

```json
{
  "@timestamp": 1752149929057,
  "firstPacket": 1041342931300,
  "lastPacket": 1041342932300,
  "length": 1000,
  "ipProtocol": 17,
  "node": "localhost",
  "source": {
    "ip": "*******",
    "port": 3267,
    "bytes": 207,
    "packets": 1,
    "mac": ["00:09:6b:88:f5:c9"]
  },
  "destination": {
    "ip": "*******", 
    "port": 2000,
    "bytes": 0,
    "packets": 0,
    "mac": ["00:e0:81:00:b0:28"]
  },
  "network": {
    "packets": 1,
    "bytes": 207
  },
  "packetPos": [-1, 24],
  "fileId": [1],
  "protocols": ["general", "udp"],
  "general": {
    "srcAddr": "*******",
    "dstAddr": "*******",
    "srcPort": 3267,
    "dstPort": 2000,
    "upBytes": 207,
    "downBytes": 0,
    "upPackets": 1,
    "downPackets": 0,
    "firstPacket": 1041342931300,
    "lastPacket": 1041342932300,
    "length": 1000,
    "proto": 17
  },
  "udp": {
    // UDP协议特定字段
  }
}
```

## 总结

✅ **完成的优化**:
1. 移除C代码中的复杂if-else判断
2. 字段映射逻辑交由Lua适配层处理  
3. C代码专注JSON构造和后处理
4. 保持完整的Arkime session格式兼容性
5. 提高代码可维护性和扩展性

🎯 **核心价值**:
- **分离关注点**: Lua处理映射，C处理构造
- **提高灵活性**: 配置化的字段映射规则
- **简化维护**: 清晰的代码结构和职责划分
