# Session必须字段逻辑修正和补全

## 问题分析

原始代码存在以下问题：
1. `protocols_array`变量未定义但被使用
2. `field_name`类型不匹配（const char* vs char*）
3. 尝试访问flow中不存在的`arkime_file_num`和`arkime_file_pos`字段
4. 缺少session必须字段的构造逻辑
5. 缺少general层字段到session第一级的映射逻辑
6. 缺少source、destination、network对象的构造

## 修正方案

### 1. 修正编译错误

**问题**: `protocols_array`未定义
**解决**: 在函数开始时创建`cJSON *protocols_array = cJSON_CreateArray()`

**问题**: `field_name`类型不匹配
**解决**: 将`char *field_name`改为`const char *field_name`

**问题**: cJSON头文件路径错误
**解决**: 修改为`#include "../../include/cJSON.h"`

### 2. 修正文件信息获取

**问题**: flow结构中没有`arkime_file_num`和`arkime_file_pos`字段
**解决**: 使用`arkime_record_get_file_pos_array()`函数从record的文件信息链表中获取

```c
// 原始错误代码
cJSON_AddItemToArray(packet_pos, cJSON_CreateNumber(-(int64_t)flow->arkime_file_num));

// 修正后代码
int64_t *pos_array = NULL;
int array_size = 0;
if (arkime_record_get_file_pos_array(record, &pos_array, &array_size) == 0 && pos_array) {
    for (int i = 0; i < array_size; i++) {
        cJSON_AddItemToArray(packet_pos, cJSON_CreateNumber(pos_array[i]));
    }
    free(pos_array);
}
```

### 3. 完善session必须字段构造

根据`pcap/session-必须字段.json`的要求，添加以下必须字段：

#### 基础字段
- `@timestamp`: 当前时间戳（毫秒）
- `node`: 节点名称（默认"localhost"）
- `ipProtocol`: IP协议号
- `length`: 会话持续时间

#### source对象
- `ip`: 源IP地址
- `port`: 源端口
- `bytes`: 源字节数
- `packets`: 源包数
- `mac`: 源MAC地址数组

#### destination对象
- `ip`: 目标IP地址
- `port`: 目标端口
- `bytes`: 目标字节数
- `packets`: 目标包数
- `mac`: 目标MAC地址数组

#### network对象
- `bytes`: 总字节数
- `packets`: 总包数

#### 其他必须字段
- `protocols`: 协议数组
- `packetPos`: 包位置数组
- `fileId`: 文件ID数组

### 4. 架构优化：Lua适配层处理字段映射

**重要改进**: 移除了C代码中的大量if-else判断，改为由Lua适配层处理字段映射，C代码只负责通用的JSON构造逻辑。

**新的架构**:
1. **Lua适配层** (`adapt_general.lua`): 负责将common字段映射到general字段
2. **C代码**: 统一处理所有字段，从general对象中提取信息构造Arkime必须字段

```c
// 简化后的C代码逻辑 - 统一处理所有字段
cJSON *proto_obj = cJSON_GetObjectItem(session, layer_name);
if (!proto_obj) {
    proto_obj = cJSON_CreateObject();
    cJSON_AddItemToObject(session, layer_name, proto_obj);
}

// 智能类型转换
char *endptr;
long long num_val = strtoll(value_str, &endptr, 10);
if (*endptr == '\0') {
    cJSON_AddNumberToObject(proto_obj, field_name, num_val);
} else {
    cJSON_AddStringToObject(proto_obj, field_name, value_str);
}

// 如果是general层，收集统计信息用于构造必须字段
if (strcmp(layer_name, "general") == 0) {
    // 收集upBytes, downBytes, srcAddr等信息
}
```

**后处理逻辑**: 从general对象中提取信息构造Arkime必须字段
```c
// 从general对象中提取信息构造source/destination对象
cJSON *general_obj = cJSON_GetObjectItem(session, "general");
if (general_obj) {
    cJSON *src_addr = cJSON_GetObjectItem(general_obj, "srcAddr");
    if (src_addr && cJSON_IsString(src_addr)) {
        cJSON_AddStringToObject(source, "ip", cJSON_GetStringValue(src_addr));
    }
    // ... 其他字段提取
}
```

### 5. 字段值智能类型转换

实现字段值的智能类型转换，数字字符串转换为JSON数字类型，其他保持字符串类型：

```c
char *endptr;
long long num_val = strtoll(value_str, &endptr, 10);
if (*endptr == '\0') {
    // 成功解析为整数
    cJSON_AddNumberToObject(target_obj, field_name, num_val);
} else {
    // 作为字符串处理
    cJSON_AddStringToObject(target_obj, field_name, value_str);
}
```

### 6. 添加必要的头文件

```c
#include "../sdtapp_interface.h"  // 包含arkime_record_get_file_pos_array函数声明
```

## 关键映射关系

### general层字段到session字段的映射

| general字段 | session字段 | 位置 | 说明 |
|------------|-------------|------|------|
| `srcAddr` | `source.ip` | source对象 | 源IP地址 |
| `dstAddr` | `destination.ip` | destination对象 | 目标IP地址 |
| `srcPort` | `source.port` | source对象 | 源端口 |
| `dstPort` | `destination.port` | destination对象 | 目标端口 |
| `proto` | `ipProtocol` | session第一级 | IP协议号 |
| `firstPacket` | `firstPacket` | session第一级 | 首包时间戳 |
| `lastPacket` | `lastPacket` | session第一级 | 末包时间戳 |
| `length` | `length` | session第一级 | 会话持续时间 |
| `upBytes` | `source.bytes` | source对象 | 上行字节数 |
| `downBytes` | `destination.bytes` | destination对象 | 下行字节数 |
| `upPackets` | `source.packets` | source对象 | 上行包数 |
| `downPackets` | `destination.packets` | destination对象 | 下行包数 |
| `srcMac` | `source.mac[]` | source对象 | 源MAC地址数组 |
| `dstMac` | `destination.mac[]` | destination对象 | 目标MAC地址数组 |
| 其他字段 | 同名字段 | session第一级 | 直接映射 |

### 非general层字段处理

非general层的字段会创建对应的协议对象，例如：
- `http`层的字段 -> `session.http`对象
- `tcp`层的字段 -> `session.tcp`对象
- `dns`层的字段 -> `session.dns`对象

## 测试验证

创建了`test_session_logic.c`测试文件，用于验证：
1. JSON格式正确性
2. 必须字段完整性
3. 字段类型转换正确性
4. general层映射逻辑正确性

## 总结

修正后的逻辑完全符合Arkime session的JSON格式要求，正确处理了：
1. ✅ general层字段到session第一级的映射
2. ✅ 必须字段的完整构造
3. ✅ 文件信息的正确获取
4. ✅ 协议数组的构造
5. ✅ 字段值的智能类型转换
6. ✅ 内存管理和错误处理

这样修正后的代码可以正确地将record中的字段信息转换为符合Arkime标准的session JSON格式。
