/*测试session JSON构建逻辑*/

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>

#include "dpi_arkime_session.h"
#include "../dpi_log.h"
#include "cJSON.h"

// 模拟全局配置
extern struct global_config g_config;

// 测试函数：验证JSON格式是否正确
int test_session_json_format(const char *json_str) {
    printf("Generated JSON:\n%s\n\n", json_str);
    
    cJSON *json = cJSON_Parse(json_str);
    if (!json) {
        printf("ERROR: Invalid JSON format\n");
        return -1;
    }
    
    // 检查必需的字段
    cJSON *timestamp = cJSON_GetObjectItem(json, "@timestamp");
    cJSON *firstPacket = cJSON_GetObjectItem(json, "firstPacket");
    cJSON *lastPacket = cJSON_GetObjectItem(json, "lastPacket");
    cJSON *length = cJSON_GetObjectItem(json, "length");
    cJSON *protocols = cJSON_GetObjectItem(json, "protocols");
    cJSON *source = cJSON_GetObjectItem(json, "source");
    cJSON *destination = cJSON_GetObjectItem(json, "destination");
    cJSON *network = cJSON_GetObjectItem(json, "network");
    cJSON *packetPos = cJSON_GetObjectItem(json, "packetPos");
    cJSON *fileId = cJSON_GetObjectItem(json, "fileId");
    cJSON *node = cJSON_GetObjectItem(json, "node");
    
    printf("Validation Results:\n");
    printf("- @timestamp: %s\n", timestamp ? "✓" : "✗");
    printf("- firstPacket: %s\n", firstPacket ? "✓" : "✗");
    printf("- lastPacket: %s\n", lastPacket ? "✓" : "✗");
    printf("- length: %s\n", length ? "✓" : "✗");
    printf("- protocols: %s\n", protocols ? "✓" : "✗");
    printf("- source: %s\n", source ? "✓" : "✗");
    printf("- destination: %s\n", destination ? "✓" : "✗");
    printf("- network: %s\n", network ? "✓" : "✗");
    printf("- packetPos: %s\n", packetPos ? "✓" : "✗");
    printf("- fileId: %s\n", fileId ? "✓" : "✗");
    printf("- node: %s\n", node ? "✓" : "✗");
    
    // 检查protocols数组
    if (protocols && cJSON_IsArray(protocols)) {
        int array_size = cJSON_GetArraySize(protocols);
        printf("- protocols array size: %d\n", array_size);
        
        for (int i = 0; i < array_size; i++) {
            cJSON *protocol = cJSON_GetArrayItem(protocols, i);
            if (cJSON_IsString(protocol)) {
                const char *proto_name = cJSON_GetStringValue(protocol);
                printf("  - protocol[%d]: %s\n", i, proto_name);
                
                // 检查对应的协议层对象是否存在
                cJSON *proto_obj = cJSON_GetObjectItem(json, proto_name);
                if (proto_obj && cJSON_IsObject(proto_obj)) {
                    printf("    - %s layer object: ✓\n", proto_name);
                    
                    // 显示协议层的字段
                    cJSON *field = NULL;
                    cJSON_ArrayForEach(field, proto_obj) {
                        if (cJSON_IsString(field)) {
                            printf("      - %s: %s\n", field->string, cJSON_GetStringValue(field));
                        }
                    }
                } else {
                    printf("    - %s layer object: ✗\n", proto_name);
                }
            }
        }
    }
    
    // 检查source对象
    if (source && cJSON_IsObject(source)) {
        cJSON *ip = cJSON_GetObjectItem(source, "ip");
        cJSON *port = cJSON_GetObjectItem(source, "port");
        cJSON *bytes = cJSON_GetObjectItem(source, "bytes");
        cJSON *packets = cJSON_GetObjectItem(source, "packets");
        
        printf("- source.ip: %s\n", ip ? "✓" : "✗");
        printf("- source.port: %s\n", port ? "✓" : "✗");
        printf("- source.bytes: %s\n", bytes ? "✓" : "✗");
        printf("- source.packets: %s\n", packets ? "✓" : "✗");
    }
    
    // 检查destination对象
    if (destination && cJSON_IsObject(destination)) {
        cJSON *ip = cJSON_GetObjectItem(destination, "ip");
        cJSON *port = cJSON_GetObjectItem(destination, "port");
        cJSON *bytes = cJSON_GetObjectItem(destination, "bytes");
        cJSON *packets = cJSON_GetObjectItem(destination, "packets");
        
        printf("- destination.ip: %s\n", ip ? "✓" : "✗");
        printf("- destination.port: %s\n", port ? "✓" : "✗");
        printf("- destination.bytes: %s\n", bytes ? "✓" : "✗");
        printf("- destination.packets: %s\n", packets ? "✓" : "✗");
    }
    
    cJSON_Delete(json);
    return 0;
}

// 测试主函数
int main() {
    printf("Testing Arkime Session JSON Format\n");
    printf("===================================\n\n");
    
    // 这里需要创建一个模拟的record来测试
    // 由于依赖较多，这个测试程序主要用于验证JSON格式
    
    // 示例期望的JSON格式
    const char *expected_json = "{"
        "\"@timestamp\": 1752149929057,"
        "\"firstPacket\": 1041342931300,"
        "\"lastPacket\": 1041342932300,"
        "\"length\": 1000,"
        "\"source\": {"
            "\"ip\": \"***********\","
            "\"port\": 80,"
            "\"bytes\": 1024,"
            "\"packets\": 10"
        "},"
        "\"destination\": {"
            "\"ip\": \"***********\","
            "\"port\": 8080,"
            "\"bytes\": 2048,"
            "\"packets\": 15"
        "},"
        "\"network\": {"
            "\"bytes\": 3072,"
            "\"packets\": 25"
        "},"
        "\"protocols\": [\"udp\", \"http\"],"
        "\"http\": {"
            "\"method\": \"PUT\""
        "},"
        "\"packetPos\": [2162688],"
        "\"fileId\": [1],"
        "\"node\": \"test-node\""
    "}";
    
    printf("Testing expected JSON format:\n");
    test_session_json_format(expected_json);
    
    return 0;
}
