From dcc36d8cb18808b06c0bab43f01dabc3eb0bbcde Mon Sep 17 00:00:00 2001
From: wuby <<EMAIL>>
Date: Tue, 22 Jul 2025 00:45:32 +0800
Subject: [PATCH] =?UTF-8?q?wuby:=E6=B7=BB=E5=8A=A0arkime=E9=80=82=E9=85=8D?=
 =?UTF-8?q?=E6=A1=86=E6=9E=B6?=
MIME-Version: 1.0
Content-Type: text/plain; charset=UTF-8
Content-Transfer-Encoding: 8bit

---
 .gitignore                      |   2 +-
 CMakeLists.txt                  |   2 +-
 etc/config.ini                  |  20 ++
 src/CMakeLists.txt              |   8 +-
 src/arkime/dpi_arkime_es.c      | 367 ++++++++++++++++++++++++
 src/arkime/dpi_arkime_es.h      |  95 +++++++
 src/arkime/dpi_arkime_field.c   | 448 +++++++++++++++++++++++++++++
 src/arkime/dpi_arkime_field.h   |  63 +++++
 src/arkime/dpi_arkime_pcap.c    | 485 ++++++++++++++++++++++++++++++++
 src/arkime/dpi_arkime_pcap.h    |  70 +++++
 src/arkime/dpi_arkime_session.c | 298 ++++++++++++++++++++
 src/arkime/dpi_arkime_session.h |  43 +++
 src/dpi_detect.c                | 301 +++++++++++++++++++-
 src/dpi_detect.h                |  25 ++
 src/dpi_main.c                  |  29 +-
 src/dpi_sdt_match.c             |   9 +
 src/dpi_tbl_log.c               |  10 +-
 src/sdtapp_interface.h          |  15 +-
 18 files changed, 2281 insertions(+), 9 deletions(-)
 create mode 100644 src/arkime/dpi_arkime_es.c
 create mode 100644 src/arkime/dpi_arkime_es.h
 create mode 100644 src/arkime/dpi_arkime_field.c
 create mode 100644 src/arkime/dpi_arkime_field.h
 create mode 100644 src/arkime/dpi_arkime_pcap.c
 create mode 100644 src/arkime/dpi_arkime_pcap.h
 create mode 100644 src/arkime/dpi_arkime_session.c
 create mode 100644 src/arkime/dpi_arkime_session.h

diff --git a/.gitignore b/.gitignore
index ea23e21f..22a87f1b 100644
--- a/.gitignore
+++ b/.gitignore
@@ -30,5 +30,5 @@ rule_error.log
 run/yaDpiSdt*
 run/vtysh
 src/run
-
+run/dpisdt_test
 
diff --git a/CMakeLists.txt b/CMakeLists.txt
index da5a3cfb..7cc551f3 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -118,7 +118,7 @@ list(APPEND dpdk_cflags
 set(ENV{PKG_CONFIG_PATH} "$ENV{PKG_CONFIG_PATH}:/usr/local/lib64/pkgconfig")
 
 add_subdirectory(src)
-add_subdirectory(test)
+#add_subdirectory(test)
 
 add_custom_target(add_proto
   COMMAND
diff --git a/etc/config.ini b/etc/config.ini
index b45faea1..c1837f0e 100644
--- a/etc/config.ini
+++ b/etc/config.ini
@@ -326,3 +326,23 @@ TBL_LOG_CONTENT_256K        = 1024   # default value: 262144
 SDT_WEB_ADDR                = ""  # web管理服务端口
 SDX_STAT_REPORT_WEB_ADDR    = ""  # 统计信息上报服务器
 SDX_WEB_CONFIG_ADDR         = ""
+
+[arkime]
+# Arkime PCAP文件管理配置
+ARKIME_ENABLE               = 1                        # 启用Arkime兼容模式 (0:禁用 1:启用)
+ARKIME_PCAP_DIR             = /opt/arkime/raw          # PCAP文件存储目录
+ARKIME_MAX_FILE_SIZE_MB     = 1024                     # 最大文件大小(MB) 默认1GB
+ARKIME_MAX_FILE_TIME_SEC    = 3600                     # 最大文件时间(秒) 默认1小时
+ARKIME_NODE_NAME            = localhost                # 捕获节点名称
+ARKIME_COMPRESSION          = none                     # 压缩方式(none/gzip/zstd) 暂不支持
+ARKIME_FLUSH_INTERVAL       = 100                     # 文件刷新间隔(包数) 每N个包刷新一次
+ARKIME_FILE_PERMISSIONS     = 0644                     # 文件权限
+ARKIME_DIR_PERMISSIONS      = 0755                     # 目录权限
+
+# Elasticsearch发送配置
+ES_ENABLE                   = 0                        # 启用ES发送 (0:禁用 1:启用)
+ES_HOST                     = localhost                # ES服务器地址
+ES_PORT                     = 9200                     # ES端口
+ES_USERNAME                 =                          # ES用户名（可选）
+ES_PASSWORD                 =                          # ES密码（可选）
+ES_TIMEOUT                  = 30                       # 超时时间（秒）
diff --git a/src/CMakeLists.txt b/src/CMakeLists.txt
index fb6ed031..03021f98 100644
--- a/src/CMakeLists.txt
+++ b/src/CMakeLists.txt
@@ -69,6 +69,7 @@ include_directories(
   ${CMAKE_SOURCE_DIR}/src/scan
   ${CMAKE_SOURCE_DIR}/src/utils
   ${CMAKE_SOURCE_DIR}/src/input
+  ${CMAKE_SOURCE_DIR}/src/arkime
   ${CMAKE_SOURCE_DIR}/src
   ${CMAKE_SOURCE_DIR}/include
   ${YA_DPDK_INCLUDE_DIRS}
@@ -182,6 +183,10 @@ add_library(${LibraryName}
     flow_flood.c
     dpi_327_common.c
     dpi_sdt_eval_field_callback.cpp
+    arkime/dpi_arkime_es.c
+    arkime/dpi_arkime_pcap.c
+    arkime/dpi_arkime_session.c
+    arkime/dpi_arkime_field.c
 )
 
 set(LIBRARY_OUTPUT_PATH    ${PROJECT_SOURCE_DIR}/lib)
@@ -211,6 +216,7 @@ target_include_directories(${LibraryName} PRIVATE
   ${CMAKE_SOURCE_DIR}/include
   ${CMAKE_SOURCE_DIR}/src
   ${CMAKE_SOURCE_DIR}/src/input
+  ${CMAKE_SOURCE_DIR}/src/arkime
   ${CMAKE_SOURCE_DIR}/include/libevent
   /usr/include
 )
@@ -310,4 +316,4 @@ list(APPEND dpi_dirs
 
 include(${CMAKE_SOURCE_DIR}/cmake/Install.cmake)
 include(${CMAKE_SOURCE_DIR}/cmake/CPack.cmake)
-add_subdirectory(test)
+#add_subdirectory(test)
diff --git a/src/arkime/dpi_arkime_es.c b/src/arkime/dpi_arkime_es.c
new file mode 100644
index 00000000..54e379e2
--- /dev/null
+++ b/src/arkime/dpi_arkime_es.c
@@ -0,0 +1,367 @@
+/*
+ * Arkime Elasticsearch发送模块
+ * 使用libcurl发送打包好的json结构到Elasticsearch
+ * 支持三种发送接口：
+ * 1. arkime_sessions3-{YYMMDD} - 会话数据
+ * 2. arkime_files_v30/_doc/localhost-{fileid} - 文件信息
+ * 3. arkime_fields - 字段定义
+ */
+
+#include <stdio.h>
+#include <stdlib.h>
+#include <string.h>
+#include <unistd.h>
+#include <pthread.h>
+#include <time.h>
+#include <curl/curl.h>
+#include "cJSON.h"
+#include "iniparser/iniparser.h"
+#include "dpi_arkime_es.h"
+#include "../dpi_log.h"
+#include "../dpi_detect.h"
+
+// 外部全局配置变量
+extern struct global_config g_config;
+
+// HTTP响应结构
+typedef struct {
+    char *data;
+    size_t size;
+} http_response_t;
+
+// 线程本地CURL句柄 - 每个线程独立的curl句柄，避免锁竞争
+static __thread CURL *tls_curl_handle = NULL;
+
+// HTTP响应回调函数
+static size_t write_response_callback(void *contents, size_t size, size_t nmemb, http_response_t *response) {
+    size_t total_size = size * nmemb;
+    char *ptr = realloc(response->data, response->size + total_size + 1);
+    if (!ptr) {
+        DPI_LOG(DPI_LOG_ERROR, "Failed to allocate memory for HTTP response");
+        return 0;
+    }
+
+    response->data = ptr;
+    memcpy(&(response->data[response->size]), contents, total_size);
+    response->size += total_size;
+    response->data[response->size] = '\0';
+
+    return total_size;
+}
+
+// 全局初始化ES模块（只初始化libcurl全局状态）
+int dpi_arkime_es_init(void) {
+    // 初始化libcurl全局状态
+    if (curl_global_init(CURL_GLOBAL_DEFAULT) != CURLE_OK) {
+        DPI_LOG(DPI_LOG_ERROR, "Failed to initialize libcurl");
+        return -1;
+    }
+
+    DPI_LOG(DPI_LOG_INFO, "Arkime ES module initialized successfully");
+    return 0;
+}
+
+// 线程本地初始化CURL句柄
+static int init_thread_curl_handle(void) {
+    if (tls_curl_handle) {
+        return 0; // 已经初始化过
+    }
+
+    // 创建线程本地CURL句柄
+    tls_curl_handle = curl_easy_init();
+    if (!tls_curl_handle) {
+        DPI_LOG(DPI_LOG_ERROR, "Failed to create thread-local CURL handle");
+        return -1;
+    }
+
+    // 设置基本选项
+    curl_easy_setopt(tls_curl_handle, CURLOPT_TIMEOUT, g_config.es_config.timeout_seconds);
+    curl_easy_setopt(tls_curl_handle, CURLOPT_CONNECTTIMEOUT, 10L);
+    curl_easy_setopt(tls_curl_handle, CURLOPT_WRITEFUNCTION, write_response_callback);
+    curl_easy_setopt(tls_curl_handle, CURLOPT_USERAGENT, "sdx_dpi_sdt/1.0");
+    curl_easy_setopt(tls_curl_handle, CURLOPT_FOLLOWLOCATION, 1L);
+    curl_easy_setopt(tls_curl_handle, CURLOPT_MAXREDIRS, 3L);
+
+    // 如果配置了用户名密码，设置认证
+    if (strlen(g_config.es_config.es_username) > 0) {
+        char userpwd[128];
+        snprintf(userpwd, sizeof(userpwd), "%s:%s",
+                g_config.es_config.es_username, g_config.es_config.es_password);
+        curl_easy_setopt(tls_curl_handle, CURLOPT_USERPWD, userpwd);
+    }
+
+    DPI_LOG(DPI_LOG_DEBUG, "Thread-local CURL handle initialized");
+    return 0;
+}
+
+// 清理线程本地CURL句柄
+void dpi_arkime_es_cleanup_thread(void) {
+    // 检查ES是否启用
+    if (!g_config.es_config.enabled) {
+        return;
+    }
+
+    if (tls_curl_handle) {
+        curl_easy_cleanup(tls_curl_handle);
+        tls_curl_handle = NULL;
+        DPI_LOG(DPI_LOG_DEBUG, "Thread-local CURL handle cleaned up");
+    }
+}
+
+// 清理ES模块（全局清理）
+void dpi_arkime_es_cleanup(void) {
+    // 检查ES是否启用
+    if (!g_config.es_config.enabled) {
+        return;
+    }
+
+    curl_global_cleanup();
+    DPI_LOG(DPI_LOG_INFO, "Arkime ES module cleaned up");
+}
+
+// 设置ES配置（无需锁保护，配置通常在初始化时设置）
+void dpi_arkime_es_set_config(const char *host, int port, const char *username,
+                             const char *password, const char *node_name,
+                             int timeout, int enabled) {
+    if (host) {
+        strncpy(g_config.es_config.es_host, host, sizeof(g_config.es_config.es_host) - 1);
+        g_config.es_config.es_host[sizeof(g_config.es_config.es_host) - 1] = '\0';
+    }
+
+    if (port > 0) {
+        g_config.es_config.es_port = port;
+    }
+
+    if (username) {
+        strncpy(g_config.es_config.es_username, username, sizeof(g_config.es_config.es_username) - 1);
+        g_config.es_config.es_username[sizeof(g_config.es_config.es_username) - 1] = '\0';
+    }
+
+    if (password) {
+        strncpy(g_config.es_config.es_password, password, sizeof(g_config.es_config.es_password) - 1);
+        g_config.es_config.es_password[sizeof(g_config.es_config.es_password) - 1] = '\0';
+    }
+
+    if (node_name) {
+        strncpy(g_config.es_config.node_name, node_name, sizeof(g_config.es_config.node_name) - 1);
+        g_config.es_config.node_name[sizeof(g_config.es_config.node_name) - 1] = '\0';
+    }
+
+    if (timeout > 0) {
+        g_config.es_config.timeout_seconds = timeout;
+    }
+
+    g_config.es_config.enabled = enabled;
+
+    DPI_LOG(DPI_LOG_INFO, "ES config updated: host=%s:%d, node=%s, enabled=%d",
+            g_config.es_config.es_host, g_config.es_config.es_port, g_config.es_config.node_name, g_config.es_config.enabled);
+}
+
+// 通用HTTP POST发送函数
+static int send_http_post(const char *url, const char *json_data, const char *content_type) {
+    if (!g_config.es_config.enabled) {
+        return 0; // 如果未启用ES，直接返回成功
+    }
+
+    // 确保线程本地CURL句柄已初始化
+    if (init_thread_curl_handle() != 0) {
+        DPI_LOG(DPI_LOG_ERROR, "Failed to initialize thread-local CURL handle");
+        return -1;
+    }
+
+    http_response_t response = {0};
+    CURLcode res;
+    long response_code = 0;
+
+    // 设置URL
+    curl_easy_setopt(tls_curl_handle, CURLOPT_URL, url);
+
+    // 设置POST数据
+    curl_easy_setopt(tls_curl_handle, CURLOPT_POSTFIELDS, json_data);
+    curl_easy_setopt(tls_curl_handle, CURLOPT_POSTFIELDSIZE, strlen(json_data));
+
+    // 设置HTTP头
+    struct curl_slist *headers = NULL;
+    char content_type_header[256];
+    snprintf(content_type_header, sizeof(content_type_header), "Content-Type: %s", content_type);
+    headers = curl_slist_append(headers, content_type_header);
+    headers = curl_slist_append(headers, "Accept: application/json");
+    curl_easy_setopt(tls_curl_handle, CURLOPT_HTTPHEADER, headers);
+
+    // 如果配置了认证，设置认证
+    if (strlen(g_config.es_config.es_username) > 0) {
+        char userpwd[128];
+        snprintf(userpwd, sizeof(userpwd), "%s:%s",
+                g_config.es_config.es_username, g_config.es_config.es_password);
+        curl_easy_setopt(tls_curl_handle, CURLOPT_USERPWD, userpwd);
+    }
+
+    // 设置响应回调
+    curl_easy_setopt(tls_curl_handle, CURLOPT_WRITEDATA, &response);
+
+    // 执行请求
+    res = curl_easy_perform(tls_curl_handle);
+
+    // 获取响应码
+    curl_easy_getinfo(tls_curl_handle, CURLINFO_RESPONSE_CODE, &response_code);
+
+    // 清理
+    curl_slist_free_all(headers);
+
+    // 检查结果
+    if (res != CURLE_OK) {
+        DPI_LOG(DPI_LOG_ERROR, "HTTP POST failed: %s", curl_easy_strerror(res));
+        if (response.data) free(response.data);
+        return -1;
+    }
+
+    if (response_code < 200 || response_code >= 300) {
+        DPI_LOG(DPI_LOG_WARNING, "HTTP POST returned code %ld for URL: %s", response_code, url);
+        DPI_LOG(DPI_LOG_DEBUG, "Response: %s", response.data ? response.data : "");
+        if (response.data) free(response.data);
+        return -1;
+    }
+
+    DPI_LOG(DPI_LOG_DEBUG, "HTTP POST successful: %ld for URL: %s", response_code, url);
+    if (response.data) free(response.data);
+    return 0;
+}
+
+// 获取当前日期字符串 (YYMMDD格式)
+static void get_date_string(char *date_str, size_t size) {
+    time_t now = time(NULL);
+    struct tm *tm_info = localtime(&now);
+    strftime(date_str, size, "%y%m%d", tm_info);
+}
+
+// 1. 发送会话数据到 arkime_sessions3-{YYMMDD} - 使用bulk格式
+int dpi_arkime_es_send_session(const char *session_json) {
+    // 检查ES是否启用
+    if (!g_config.es_config.enabled) {
+        return 0; // ES未启用时直接返回成功
+    }
+
+    if (!session_json || strlen(session_json) == 0) {
+        DPI_LOG(DPI_LOG_WARNING, "Empty session JSON data");
+        return -1;
+    }
+
+    char date_str[16];
+    get_date_string(date_str, sizeof(date_str));
+
+    // 构造bulk格式数据
+    char *bulk_data = malloc(strlen(session_json) + 256);
+    if (!bulk_data) {
+        DPI_LOG(DPI_LOG_ERROR, "Failed to allocate memory for bulk data");
+        return -1;
+    }
+
+    // 构造bulk header
+    int bulk_len = snprintf(bulk_data, strlen(session_json) + 256,
+                           "{\"index\":{\"_index\":\"arkime_sessions3-%s\"}}\n%s\n",
+                           date_str, session_json);
+
+    // 使用bulk API发送
+    int result = dpi_arkime_es_send_bulk_data(bulk_data);
+
+    free(bulk_data);
+    return result;
+}
+
+// 2. 发送文件信息到 arkime_files_v30/_doc/{nodename}-{fileid}
+int dpi_arkime_es_send_file_info(uint64_t file_id, const char *file_json) {
+    // 检查ES是否启用
+    if (!g_config.es_config.enabled) {
+        return 0; // ES未启用时直接返回成功
+    }
+
+    if (!file_json || strlen(file_json) == 0) {
+        DPI_LOG(DPI_LOG_WARNING, "Empty file JSON data");
+        return -1;
+    }
+
+    char url[512];
+    snprintf(url, sizeof(url), "http://%s:%d/arkime_files_v30/_doc/%s-%lu",
+             g_config.es_config.es_host, g_config.es_config.es_port, g_config.es_config.node_name, file_id);
+
+    DPI_LOG(DPI_LOG_DEBUG, "Sending file info to: %s", url);
+    return send_http_post(url, file_json, "application/json");
+}
+
+// 3. 发送字段定义到 arkime_fields (单个字段) - 纯发送功能
+int dpi_arkime_es_send_field_definition(const char *field_id, const char *field_json) {
+    // 检查ES是否启用
+    if (!g_config.es_config.enabled) {
+        return 0; // ES未启用时直接返回成功
+    }
+
+    if (!field_id || !field_json || strlen(field_json) == 0) {
+        DPI_LOG(DPI_LOG_WARNING, "Invalid field definition data");
+        return -1;
+    }
+
+    char url[512];
+    snprintf(url, sizeof(url), "http://%s:%d/arkime_fields/_doc/%s",
+             g_config.es_config.es_host, g_config.es_config.es_port, field_id);
+
+    DPI_LOG(DPI_LOG_DEBUG, "Sending field definition to: %s", url);
+    return send_http_post(url, field_json, "application/json");
+}
+
+// 批量发送预构造的bulk数据到ES - 纯发送功能
+int dpi_arkime_es_send_bulk_data(const char *bulk_data) {
+    // 检查ES是否启用
+    if (!g_config.es_config.enabled) {
+        return 0; // ES未启用时直接返回成功
+    }
+
+    if (!bulk_data || strlen(bulk_data) == 0) {
+        DPI_LOG(DPI_LOG_WARNING, "Invalid bulk data");
+        return -1;
+    }
+
+    char url[512];
+    snprintf(url, sizeof(url), "http://%s:%d/_bulk",
+             g_config.es_config.es_host, g_config.es_config.es_port);
+
+    DPI_LOG(DPI_LOG_DEBUG, "Sending bulk data to: %s", url);
+    return send_http_post(url, bulk_data, "application/x-ndjson");
+}
+
+// 从配置文件初始化ES模块配置
+int dpi_arkime_es_init_config(void *ini) {
+    if (!ini) {
+        DPI_LOG(DPI_LOG_WARNING, "No configuration provided for ES module");
+        return 0;
+    }
+
+    // 这里需要根据实际的配置文件解析库来实现
+    // 假设使用iniparser库
+    dictionary *config = (dictionary *)ini;
+
+    // 读取ES配置
+    const char *es_host = iniparser_getstring(config, "arkime:ES_HOST", "localhost");
+    int es_port = iniparser_getint(config, "arkime:ES_PORT", 9200);
+    const char *es_username = iniparser_getstring(config, "arkime:ES_USERNAME", "");
+    const char *es_password = iniparser_getstring(config, "arkime:ES_PASSWORD", "");
+    const char *node_name = iniparser_getstring(config, "arkime:ARKIME_NODE_NAME", "localhost");
+    int timeout = iniparser_getint(config, "arkime:ES_TIMEOUT", 30);
+    int enabled = iniparser_getint(config, "arkime:ES_ENABLE", 0);
+
+    // 设置配置
+    dpi_arkime_es_set_config(es_host, es_port, es_username, es_password,
+                             node_name, timeout, enabled);
+
+    // 如果启用了ES，初始化模块
+    if (enabled) {
+        if (dpi_arkime_es_init() != 0) {
+            DPI_LOG(DPI_LOG_ERROR, "Failed to initialize ES module");
+            return -1;
+        }
+        DPI_LOG(DPI_LOG_INFO, "ES module initialized with config: %s:%d", es_host, es_port);
+    } else {
+        DPI_LOG(DPI_LOG_INFO, "ES module disabled in configuration");
+    }
+
+    return 0;
+}
diff --git a/src/arkime/dpi_arkime_es.h b/src/arkime/dpi_arkime_es.h
new file mode 100644
index 00000000..c22434c2
--- /dev/null
+++ b/src/arkime/dpi_arkime_es.h
@@ -0,0 +1,95 @@
+/*
+ * Arkime Elasticsearch发送模块头文件
+ * 支持三种发送接口：
+ * 1. arkime_sessions3-{YYMMDD} - 会话数据
+ * 2. arkime_files_v30/_doc/localhost-{fileid} - 文件信息
+ * 3. arkime_fields - 字段定义
+ */
+
+#ifndef DPI_ARKIME_ES_H
+#define DPI_ARKIME_ES_H
+
+#include <stdint.h>
+#include <arpa/inet.h>
+
+// 前向声明结构体
+struct flow_info;
+struct ProtoRecord;
+typedef struct ProtoRecord precord_t;
+
+#ifdef __cplusplus
+extern "C" {
+#endif
+
+/**
+ * 初始化ES模块
+ * @return 0成功，-1失败
+ */
+int dpi_arkime_es_init(void);
+
+/**
+ * 清理ES模块（全局清理）
+ */
+void dpi_arkime_es_cleanup(void);
+
+/**
+ * 清理线程本地CURL句柄
+ */
+void dpi_arkime_es_cleanup_thread(void);
+
+/**
+ * 设置ES配置
+ * @param host ES服务器地址
+ * @param port ES端口
+ * @param username ES用户名（可为NULL）
+ * @param password ES密码（可为NULL）
+ * @param node_name 节点名称
+ * @param timeout 超时时间（秒）
+ * @param enabled 是否启用ES发送
+ */
+void dpi_arkime_es_set_config(const char *host, int port, const char *username, 
+                             const char *password, const char *node_name, 
+                             int timeout, int enabled);
+
+/**
+ * 发送会话数据到 arkime_sessions3-{YYMMDD}
+ * @param session_json 会话数据的JSON字符串
+ * @return 0成功，-1失败
+ */
+int dpi_arkime_es_send_session(const char *session_json);
+
+/**
+ * 发送文件信息到 arkime_files_v30/_doc/{nodename}-{fileid}
+ * @param file_id 文件ID
+ * @param file_json 文件信息的JSON字符串
+ * @return 0成功，-1失败
+ */
+int dpi_arkime_es_send_file_info(uint64_t file_id, const char *file_json);
+
+/**
+ * 发送字段定义到 arkime_fields
+ * @param field_id 字段ID
+ * @param field_json 字段定义的JSON字符串
+ * @return 0成功，-1失败
+ */
+int dpi_arkime_es_send_field_definition(const char *field_id, const char *field_json);
+
+/**
+ * 发送预构造的bulk数据到ES
+ * @param bulk_data 预构造的bulk格式数据
+ * @return 0成功，-1失败
+ */
+int dpi_arkime_es_send_bulk_data(const char *bulk_data);
+
+/**
+ * 从配置文件初始化ES模块配置
+ * @param ini 配置文件字典
+ * @return 0成功，-1失败
+ */
+int dpi_arkime_es_init_config(void *ini);
+
+#ifdef __cplusplus
+}
+#endif
+
+#endif /* DPI_ARKIME_ES_H */
diff --git a/src/arkime/dpi_arkime_field.c b/src/arkime/dpi_arkime_field.c
new file mode 100644
index 00000000..5a103fb7
--- /dev/null
+++ b/src/arkime/dpi_arkime_field.c
@@ -0,0 +1,448 @@
+/*
+ * Arkime字段注册模块
+ * 遍历record注册过的字段，生成ES字段注册消息
+ * 字段格式：
+ * - type: 全部为termfield
+ * - _id: layer.field
+ * - friendlyName: field_name
+ * - group: layer名
+ * - help: 不填充
+ * - dbField2: 与_id一样
+ */
+
+#include <stdio.h>
+#include <stdlib.h>
+#include <string.h>
+#include <unistd.h>
+#include <pthread.h>
+#include "cJSON.h"
+#include "dpi_arkime_field.h"
+#include "dpi_arkime_es.h"
+#include "../dpi_log.h"
+#include "../dpi_detect.h"
+#include "../dpi_pschema.h"
+#include "../dpi_lua_adapt.h"
+
+// 外部全局配置变量
+extern struct global_config g_config;
+
+// 字段注册状态 - 使用原子操作替代互斥锁
+static volatile int g_fields_registered = 0;
+
+// 线程本地字段注册标志
+static __thread int tls_fields_registered = 0;
+
+// 创建字段定义JSON
+static char* create_arkime_field_json(const char *layer_name, const char *field_name) {
+    cJSON *field_def = cJSON_CreateObject();
+    if (!field_def) {
+        return NULL;
+    }
+    
+    // 设置字段属性
+    cJSON_AddStringToObject(field_def, "friendlyName", field_name);
+    cJSON_AddStringToObject(field_def, "group", layer_name);
+    cJSON_AddStringToObject(field_def, "help", "");  // 不填充help
+    cJSON_AddStringToObject(field_def, "type", "termfield");  // 全部为termfield
+    
+    // 构造dbField2 (layer.field格式)
+    char dbfield2[256];
+    snprintf(dbfield2, sizeof(dbfield2), "%s.%s", layer_name, field_name);
+    cJSON_AddStringToObject(field_def, "dbField2", dbfield2);
+    
+    char *json_string = cJSON_PrintUnformatted(field_def);
+    cJSON_Delete(field_def);
+    
+    return json_string;
+}
+
+// 创建批量字段注册的bulk数据
+static char* create_bulk_field_data(const char **field_ids, const char **field_jsons, int count) {
+    if (!field_ids || !field_jsons || count <= 0) {
+        return NULL;
+    }
+
+    // 计算总的bulk数据大小
+    size_t total_size = 0;
+    for (int i = 0; i < count; i++) {
+        if (field_ids[i] && field_jsons[i]) {
+            total_size += strlen(field_ids[i]) + strlen(field_jsons[i]) + 100; // 预留空间
+        }
+    }
+
+    char *bulk_data = malloc(total_size);
+    if (!bulk_data) {
+        return NULL;
+    }
+
+    bulk_data[0] = '\0';
+    int success_count = 0;
+
+    // 构造批量请求数据
+    for (int i = 0; i < count; i++) {
+        if (!field_ids[i] || !field_jsons[i]) {
+            continue;
+        }
+
+        // 构造索引元数据
+        cJSON *index_meta = cJSON_CreateObject();
+        cJSON *index_obj = cJSON_CreateObject();
+        cJSON_AddStringToObject(index_obj, "_index", "arkime_fields");
+        cJSON_AddStringToObject(index_obj, "_id", field_ids[i]);
+        cJSON_AddItemToObject(index_meta, "index", index_obj);
+
+        char *index_line = cJSON_PrintUnformatted(index_meta);
+        if (index_line) {
+            strcat(bulk_data, index_line);
+            strcat(bulk_data, "\n");
+            strcat(bulk_data, field_jsons[i]);
+            strcat(bulk_data, "\n");
+            success_count++;
+            free(index_line);
+        }
+        cJSON_Delete(index_meta);
+    }
+
+    if (success_count == 0) {
+        free(bulk_data);
+        return NULL;
+    }
+
+    return bulk_data;
+}
+
+// 注册单个协议的字段 (批量发送)
+static int register_proto_fields(pschema_t *schema) {
+    if (!schema) {
+        return -1;
+    }
+
+    const char *proto_name = pschema_get_proto_name(schema);
+    if (!proto_name) {
+        return -1;
+    }
+
+    // 跳过一些特殊的协议
+    if (strcmp(proto_name, "common") == 0 ||
+        strcmp(proto_name, "327_common") == 0 ||
+        strcmp(proto_name, "link") == 0) {
+        return 0;
+    }
+
+    int field_count = 0;
+    int max_fields = 100; // 最大批量发送字段数
+    char **field_ids = malloc(max_fields * sizeof(char*));
+    char **field_jsons = malloc(max_fields * sizeof(char*));
+    int current_batch = 0;
+
+    if (!field_ids || !field_jsons) {
+        DPI_LOG(DPI_LOG_ERROR, "Failed to allocate memory for field arrays");
+        if (field_ids) free(field_ids);
+        if (field_jsons) free(field_jsons);
+        return -1;
+    }
+
+    // 遍历协议的所有字段
+    for (pfield_desc_t *fdesc = pschema_fdesc_get_first(schema);
+         fdesc != NULL;
+         fdesc = pschema_fdesc_get_next(schema, fdesc)) {
+
+        const char *field_name = pfdesc_get_name(fdesc);
+        if (!field_name) {
+            continue;
+        }
+
+        // 构造字段ID (layer.field格式)
+        char *field_id = malloc(256);
+        if (!field_id) {
+            continue;
+        }
+        snprintf(field_id, 256, "%s.%s", proto_name, field_name);
+
+        // 创建字段定义JSON
+        char *field_json = create_arkime_field_json(proto_name, field_name);
+        if (!field_json) {
+            DPI_LOG(DPI_LOG_ERROR, "Failed to create field JSON for %s.%s", proto_name, field_name);
+            free(field_id);
+            continue;
+        }
+
+        // 添加到批量数组
+        field_ids[current_batch] = field_id;
+        field_jsons[current_batch] = field_json;
+        current_batch++;
+        field_count++;
+
+        // 如果达到批量上限，发送并重置
+        if (current_batch >= max_fields) {
+            // 构造bulk数据
+            char *bulk_data = create_bulk_field_data((const char**)field_ids,
+                                                    (const char**)field_jsons,
+                                                    current_batch);
+            if (bulk_data) {
+                int result = dpi_arkime_es_send_bulk_data(bulk_data);
+                if (result == 0) {
+                    DPI_LOG(DPI_LOG_INFO, "Registered %d fields in batch for protocol: %s",
+                            current_batch, proto_name);
+                } else {
+                    DPI_LOG(DPI_LOG_WARNING, "Failed to register batch fields for protocol: %s",
+                            proto_name);
+                }
+                free(bulk_data);
+            }
+
+            // 释放内存
+            for (int i = 0; i < current_batch; i++) {
+                free(field_ids[i]);
+                free(field_jsons[i]);
+            }
+            current_batch = 0;
+        }
+    }
+
+    // 发送剩余的字段
+    if (current_batch > 0) {
+        // 构造bulk数据
+        char *bulk_data = create_bulk_field_data((const char**)field_ids,
+                                                (const char**)field_jsons,
+                                                current_batch);
+        if (bulk_data) {
+            int result = dpi_arkime_es_send_bulk_data(bulk_data);
+            if (result == 0) {
+                DPI_LOG(DPI_LOG_INFO, "Registered %d remaining fields for protocol: %s",
+                        current_batch, proto_name);
+            } else {
+                DPI_LOG(DPI_LOG_WARNING, "Failed to register remaining fields for protocol: %s",
+                        proto_name);
+            }
+            free(bulk_data);
+        }
+
+        // 释放内存
+        for (int i = 0; i < current_batch; i++) {
+            free(field_ids[i]);
+            free(field_jsons[i]);
+        }
+    }
+
+    free(field_ids);
+    free(field_jsons);
+
+    if (field_count > 0) {
+        DPI_LOG(DPI_LOG_INFO, "Registered total %d fields for protocol: %s", field_count, proto_name);
+    }
+
+    return field_count;
+}
+
+// 注册lua_adapt后的协议字段
+int dpi_arkime_register_lua_adapt_fields(void) {
+    // 检查ES是否启用
+    if (!g_config.es_config.enabled) {
+        DPI_LOG(DPI_LOG_DEBUG, "Arkime ES module disabled, skipping lua adapt fields registration");
+        return 0;
+    }
+
+    extern padapt_controller_t *g_padapt_controller;
+
+    if (!g_padapt_controller) {
+        DPI_LOG(DPI_LOG_WARNING, "Lua adapt controller not initialized");
+        return -1;
+    }
+
+    // 获取lua_adapt后的schema数据库
+    pschema_db_t *adapted_db = padapt_controller_get_adapted_schema_db(g_padapt_controller);
+    if (!adapted_db) {
+        DPI_LOG(DPI_LOG_WARNING, "No adapted schema database available");
+        return -1;
+    }
+
+    DPI_LOG(DPI_LOG_INFO, "Starting lua_adapt field registration to Elasticsearch");
+
+    int total_fields = 0;
+    int proto_count = 0;
+
+    // 遍历lua_adapt后的所有协议
+    for (pschema_t *schema = pschema_get_first(adapted_db);
+         schema != NULL;
+         schema = pschema_get_next(adapted_db, schema)) {
+
+        int field_count = register_proto_fields(schema);
+        if (field_count > 0) {
+            total_fields += field_count;
+            proto_count++;
+        }
+    }
+
+    DPI_LOG(DPI_LOG_INFO, "Lua_adapt field registration completed: %d fields from %d protocols",
+            total_fields, proto_count);
+
+    return total_fields;
+}
+
+// 注册所有已注册协议的字段 - 使用原子操作和线程本地标志
+int dpi_arkime_register_all_fields(void) {
+    // 检查ES是否启用
+    if (!g_config.es_config.enabled) {
+        DPI_LOG(DPI_LOG_DEBUG, "Arkime ES module disabled, skipping all fields registration");
+        return 0;
+    }
+
+    // 检查线程本地标志，避免同一线程重复注册
+    if (tls_fields_registered) {
+        DPI_LOG(DPI_LOG_DEBUG, "Fields already registered by this thread, skipping");
+        return 0;
+    }
+
+    // 检查全局标志，使用原子操作避免竞争
+    if (__sync_val_compare_and_swap(&g_fields_registered, 0, 1) != 0) {
+        // 已经被其他线程注册过
+        tls_fields_registered = 1; // 设置线程本地标志
+        DPI_LOG(DPI_LOG_INFO, "Fields already registered by another thread, skipping");
+        return 0;
+    }
+
+    // 检查ES是否启用
+    if (!g_config.es_config.enabled) {
+        // 重置全局标志，允许下次尝试
+        __sync_bool_compare_and_swap(&g_fields_registered, 1, 0);
+        DPI_LOG(DPI_LOG_INFO, "ES disabled, skipping field registration");
+        return 0;
+    }
+
+    DPI_LOG(DPI_LOG_INFO, "Starting field registration to Elasticsearch");
+
+    int total_fields = 0;
+    int proto_count = 0;
+
+    // 优先使用lua_adapt后的字段
+    int lua_adapt_fields = dpi_arkime_register_lua_adapt_fields();
+    if (lua_adapt_fields > 0) {
+        total_fields += lua_adapt_fields;
+        DPI_LOG(DPI_LOG_INFO, "Registered %d lua_adapt fields", lua_adapt_fields);
+    } else {
+        // 如果lua_adapt失败，回退到原始schema
+        DPI_LOG(DPI_LOG_INFO, "Lua_adapt registration failed, using original schemas");
+
+        // 遍历所有已注册的协议
+        for (pschema_t *schema = dpi_pschema_get_first();
+             schema != NULL;
+             schema = dpi_pschema_get_next(schema)) {
+
+            int field_count = register_proto_fields(schema);
+            if (field_count > 0) {
+                total_fields += field_count;
+                proto_count++;
+            }
+        }
+    }
+
+    // 设置线程本地标志
+    tls_fields_registered = 1;
+
+    DPI_LOG(DPI_LOG_INFO, "Field registration completed: %d total fields", total_fields);
+
+    return total_fields;
+}
+
+// 注册单个协议的字段（用于动态注册）
+int dpi_arkime_register_proto_fields(const char *proto_name) {
+    // 检查ES是否启用
+    if (!g_config.es_config.enabled) {
+        DPI_LOG(DPI_LOG_DEBUG, "Arkime ES module disabled, skipping proto fields registration for %s", proto_name ? proto_name : "NULL");
+        return 0;
+    }
+
+    if (!proto_name) {
+        DPI_LOG(DPI_LOG_ERROR, "Invalid protocol name");
+        return -1;
+    }
+    
+    // 检查ES是否启用
+    if (!g_config.es_config.enabled) {
+        DPI_LOG(DPI_LOG_DEBUG, "ES disabled, skipping field registration for %s", proto_name);
+        return 0;
+    }
+    
+    // 获取协议schema
+    pschema_t *schema = dpi_pschema_get_proto(proto_name);
+    if (!schema) {
+        DPI_LOG(DPI_LOG_WARNING, "Protocol schema not found: %s", proto_name);
+        return -1;
+    }
+    
+    DPI_LOG(DPI_LOG_DEBUG, "Registering fields for protocol: %s", proto_name);
+    
+    int field_count = register_proto_fields(schema);
+    
+    if (field_count > 0) {
+        DPI_LOG(DPI_LOG_INFO, "Registered %d fields for protocol: %s", field_count, proto_name);
+    }
+    
+    return field_count;
+}
+
+// 重置字段注册状态（用于测试）- 使用原子操作
+void dpi_arkime_reset_field_registration(void) {
+    // 检查ES是否启用
+    if (!g_config.es_config.enabled) {
+        DPI_LOG(DPI_LOG_DEBUG, "Arkime ES module disabled, skipping field registration reset");
+        return;
+    }
+
+    __sync_bool_compare_and_swap(&g_fields_registered, 1, 0);
+    tls_fields_registered = 0; // 重置线程本地标志
+    DPI_LOG(DPI_LOG_DEBUG, "Field registration status reset");
+}
+
+// 检查字段是否已注册 - 使用原子读取
+int dpi_arkime_is_fields_registered(void) {
+    // 检查ES是否启用
+    if (!g_config.es_config.enabled) {
+        return 1; // ES未启用时返回已注册状态，避免重复检查
+    }
+
+    return __sync_fetch_and_add(&g_fields_registered, 0); // 原子读取
+}
+
+// 获取字段注册统计信息
+int dpi_arkime_get_field_stats(int *proto_count, int *field_count) {
+    if (!proto_count || !field_count) {
+        return -1;
+    }
+    
+    *proto_count = 0;
+    *field_count = 0;
+    
+    // 遍历所有协议统计字段数量
+    for (pschema_t *schema = dpi_pschema_get_first(); 
+         schema != NULL; 
+         schema = dpi_pschema_get_next(schema)) {
+        
+        const char *proto_name = pschema_get_proto_name(schema);
+        if (!proto_name) {
+            continue;
+        }
+        
+        // 跳过特殊协议
+        if (strcmp(proto_name, "common") == 0 || 
+            strcmp(proto_name, "327_common") == 0 ||
+            strcmp(proto_name, "link") == 0) {
+            continue;
+        }
+        
+        int fields_in_proto = 0;
+        for (pfield_desc_t *fdesc = pschema_fdesc_get_first(schema);
+             fdesc != NULL;
+             fdesc = pschema_fdesc_get_next(schema, fdesc)) {
+            fields_in_proto++;
+        }
+        
+        if (fields_in_proto > 0) {
+            (*proto_count)++;
+            (*field_count) += fields_in_proto;
+        }
+    }
+    
+    return 0;
+}
diff --git a/src/arkime/dpi_arkime_field.h b/src/arkime/dpi_arkime_field.h
new file mode 100644
index 00000000..1a31ecf2
--- /dev/null
+++ b/src/arkime/dpi_arkime_field.h
@@ -0,0 +1,63 @@
+/*
+ * Arkime字段注册模块头文件
+ * 遍历record注册过的字段，生成ES字段注册消息
+ */
+
+#ifndef DPI_ARKIME_FIELD_H
+#define DPI_ARKIME_FIELD_H
+
+#include <stdint.h>
+
+#ifdef __cplusplus
+extern "C" {
+#endif
+
+/**
+ * 注册所有已注册协议的字段到Elasticsearch
+ * 遍历所有通过record注册的协议和字段，生成ES字段定义并发送
+ * 字段格式：
+ * - type: 全部为termfield
+ * - _id: layer.field
+ * - friendlyName: field_name
+ * - group: layer名
+ * - help: 不填充
+ * - dbField2: 与_id一样
+ * 
+ * @return 注册的字段总数，失败返回-1
+ */
+int dpi_arkime_register_all_fields(void);
+
+/**
+ * 注册指定协议的字段到Elasticsearch
+ * 用于动态注册新协议的字段
+ * 
+ * @param proto_name 协议名称
+ * @return 注册的字段数量，失败返回-1
+ */
+int dpi_arkime_register_proto_fields(const char *proto_name);
+
+/**
+ * 重置字段注册状态
+ * 主要用于测试，允许重新注册字段
+ */
+void dpi_arkime_reset_field_registration(void);
+
+/**
+ * 检查字段是否已注册
+ * @return 1已注册，0未注册
+ */
+int dpi_arkime_is_fields_registered(void);
+
+/**
+ * 获取字段注册统计信息
+ * @param proto_count 输出参数，协议数量
+ * @param field_count 输出参数，字段总数
+ * @return 0成功，-1失败
+ */
+int dpi_arkime_get_field_stats(int *proto_count, int *field_count);
+
+#ifdef __cplusplus
+}
+#endif
+
+#endif /* DPI_ARKIME_FIELD_H */
diff --git a/src/arkime/dpi_arkime_pcap.c b/src/arkime/dpi_arkime_pcap.c
new file mode 100644
index 00000000..6d687b5b
--- /dev/null
+++ b/src/arkime/dpi_arkime_pcap.c
@@ -0,0 +1,485 @@
+/*保存pcap - Arkime兼容的PCAP文件管理模块*/
+
+#include <stdio.h>
+#include <stdlib.h>
+#include <string.h>
+#include <unistd.h>
+#include <time.h>
+#include <sys/stat.h>
+#include <sys/time.h>
+#include <pthread.h>
+#include <stdint.h>
+
+#include "dpi_arkime_pcap.h"
+#include "dpi_arkime_es.h"
+#include "../dpi_log.h"
+#include "../dpi_detect.h"
+#include "cJSON.h"
+
+extern struct global_config g_config;
+
+
+// PCAP文件头结构定义
+struct pcap_file_header {
+    uint32_t magic;
+    uint16_t version_major;
+    uint16_t version_minor;
+    int32_t  thiszone;
+    uint32_t sigfigs;
+    uint32_t snaplen;
+    uint32_t linktype;
+};
+
+// PCAP包头结构定义
+struct pcap_pkthdr {
+    uint32_t ts_sec;
+    uint32_t ts_usec;
+    uint32_t caplen;
+    uint32_t len;
+};
+
+// 全局文件序号管理 - 使用原子操作替代互斥锁
+static volatile uint64_t g_global_file_num = 0;
+
+/*
+ * pthread_mutex优化：移除g_file_num_mutex
+ * 原因：使用原子操作__sync_fetch_and_add替代互斥锁
+ * 优势：更高的性能，无锁竞争，原子性保证
+ */
+
+// 全局文件信息存储结构
+typedef struct arkime_file_info {
+    uint64_t num;                    // 文件ID（唯一标识）
+    char name[512];                  // 文件完整路径
+    uint64_t first;                  // 文件首包时间戳（毫秒）
+    char node[64];                   // 捕获节点名称
+    uint64_t filesize;               // 文件大小（字节）
+    int locked;                      // 锁定状态：1=处理中，0=完成
+    time_t create_time;              // 文件创建时间
+    FILE *fp;                        // 文件指针
+    uint32_t packet_count;           // 包计数
+    struct arkime_file_info *next;   // 链表指针
+} arkime_file_info_t;
+
+// 全局文件信息链表
+static arkime_file_info_t *g_file_list_head = NULL;
+static pthread_mutex_t g_file_list_mutex = PTHREAD_MUTEX_INITIALIZER;
+
+/*
+ * pthread_mutex分析：g_file_list_mutex
+ * 用途：保护全局文件信息链表的并发访问
+ * 必要性：防止多线程同时修改链表结构导致数据竞争和内存错误
+ * 优化建议：可以考虑使用无锁数据结构或读写锁，减少锁竞争
+ */
+
+// 当前活跃文件（每个线程一个）
+static __thread arkime_file_info_t *current_file = NULL;
+
+// 配置参数（从配置文件读取或使用默认值）
+static uint64_t max_file_size_bytes = 1024 * 1024 * 1024;  // 默认1GB
+static uint32_t max_file_time_seconds = 3600;               // 默认1小时
+static char pcap_dir[512] = "/tmp/arkime/raw";              // 默认PCAP目录
+static char node_name[64] = "localhost";                    // 默认节点名
+
+//申请一个全局文件序号,线程安全 - 使用原子操作
+uint64_t dpi_arkime_file_num_creat(){
+    // 检查ES是否启用
+    if (!g_config.es_config.enabled) {
+        return __sync_add_and_fetch(&g_global_file_num, 1);
+    }
+
+    // 使用原子操作递增并返回新值，确保线程安全且性能更好
+    return __sync_add_and_fetch(&g_global_file_num, 1);
+}
+
+// 创建文件信息JSON数据
+static char* create_file_info_json(arkime_file_info_t *file_info) {
+    if (!file_info) {
+        return NULL;
+    }
+
+    cJSON *file_json = cJSON_CreateObject();
+    if (!file_json) {
+        return NULL;
+    }
+
+    cJSON_AddNumberToObject(file_json, "num", file_info->num);
+    cJSON_AddStringToObject(file_json, "name", file_info->name);
+    cJSON_AddNumberToObject(file_json, "first", file_info->first);
+    cJSON_AddStringToObject(file_json, "node", g_config.es_config.node_name);
+    cJSON_AddNumberToObject(file_json, "filesize", file_info->filesize);
+    cJSON_AddNumberToObject(file_json, "locked", file_info->locked);
+
+    char *json_string = cJSON_PrintUnformatted(file_json);
+    cJSON_Delete(file_json);
+
+    return json_string;
+}
+
+// 发送文件信息到ES
+static int send_file_info_to_es(arkime_file_info_t *file_info) {
+    if (!file_info || !g_config.es_config.enabled) {
+        return 0; // ES未启用时直接返回成功
+    }
+
+    char *file_json = create_file_info_json(file_info);
+    if (!file_json) {
+        DPI_LOG(DPI_LOG_ERROR, "Failed to create file info JSON for file %lu", file_info->num);
+        return -1;
+    }
+
+    int result = dpi_arkime_es_send_file_info(file_info->num, file_json);
+    if (result == 0) {
+        DPI_LOG(DPI_LOG_DEBUG, "Sent file info to ES: file_num=%lu, name=%s",
+                file_info->num, file_info->name);
+    } else {
+        DPI_LOG(DPI_LOG_WARNING, "Failed to send file info to ES: file_num=%lu", file_info->num);
+    }
+
+    free(file_json);
+    return result;
+}
+
+// 初始化配置参数
+void dpi_arkime_pcap_init_config(dictionary *ini) {
+    // 检查ES是否启用
+    if (!g_config.es_config.enabled) {
+        DPI_LOG(DPI_LOG_DEBUG, "Arkime ES module disabled, skipping pcap config initialization");
+        return;
+    }
+        // 从[arkime]配置段读取参数
+        // 读取配置参数
+        const char *pcap_dir_str = iniparser_getstring(ini, "arkime:ARKIME_PCAP_DIR", "/opt/arkime/raw");
+        uint32_t max_file_size_mb = iniparser_getint(ini, "arkime:ARKIME_MAX_FILE_SIZE_MB", 1024);
+        uint32_t max_file_time_sec = iniparser_getint(ini, "arkime:ARKIME_MAX_FILE_TIME_SEC", 3600);
+        const char *node_name_str = iniparser_getstring(ini, "arkime:ARKIME_NODE_NAME", "localhost");
+        uint32_t flush_interval = iniparser_getint(ini, "arkime:ARKIME_FLUSH_INTERVAL", 100);
+
+        // 设置全局变量
+        max_file_size_bytes = (uint64_t)max_file_size_mb * 1024 * 1024;
+        max_file_time_seconds = max_file_time_sec;
+        snprintf(pcap_dir, sizeof(pcap_dir), "%s", pcap_dir_str);
+        snprintf(node_name, sizeof(node_name), "%s", node_name_str);
+
+        printf("Arkime PCAP配置:\n");
+        printf("  - 存储目录: %s\n", pcap_dir);
+        printf("  - 最大文件大小: %u MB\n", max_file_size_mb);
+        printf("  - 最大文件时间: %u 秒\n", max_file_time_sec);
+        printf("  - 节点名称: %s\n", node_name);
+        printf("  - 刷新间隔: %u 包\n", flush_interval);
+
+    // 确保PCAP目录存在
+    struct stat st = {0};
+    if (stat(pcap_dir, &st) == -1) {
+        if (mkdir(pcap_dir, 0755) == 0) {
+            printf("创建PCAP目录: %s\n", pcap_dir);
+        } else {
+            printf("错误: 无法创建PCAP目录: %s\n", pcap_dir);
+        }
+    }
+}
+
+// 创建新的PCAP文件
+arkime_file_info_t* dpi_arkime_create_pcap_file(uint64_t first_packet_time) {
+    arkime_file_info_t *file_info = malloc(sizeof(arkime_file_info_t));
+    if (!file_info) {
+        return NULL;
+    }
+
+    memset(file_info, 0, sizeof(arkime_file_info_t));
+
+    // 生成文件ID
+    file_info->num = dpi_arkime_file_num_creat();
+
+    // 生成文件名（格式：节点名-文件ID-时间戳.pcap）
+    time_t now = time(NULL);
+    struct tm *tm_info = localtime(&now);
+    snprintf(file_info->name, sizeof(file_info->name),
+             "%s/%s-%lu-%04d%02d%02d-%02d%02d%02d.pcap",
+             pcap_dir, node_name, file_info->num,
+             tm_info->tm_year + 1900, tm_info->tm_mon + 1, tm_info->tm_mday,
+             tm_info->tm_hour, tm_info->tm_min, tm_info->tm_sec);
+
+    // 设置其他字段
+    file_info->first = first_packet_time;
+    snprintf(file_info->node, sizeof(file_info->node), "%s", node_name);
+    file_info->filesize = 0;
+    file_info->locked = 1;  // 创建时锁定
+    file_info->create_time = now;
+    file_info->packet_count = 0;
+    file_info->next = NULL;
+
+    // 打开文件
+    file_info->fp = fopen(file_info->name, "wb");
+    if (!file_info->fp) {
+        free(file_info);
+        return NULL;
+    }
+
+    // 写入PCAP文件头
+    struct pcap_file_header pcap_hdr;
+    pcap_hdr.magic = 0xa1b2c3d4;
+    pcap_hdr.version_major = 2;
+    pcap_hdr.version_minor = 4;
+    pcap_hdr.thiszone = 0;
+    pcap_hdr.sigfigs = 0;
+    pcap_hdr.snaplen = 65535;
+    pcap_hdr.linktype = 1;  // DLT_EN10MB
+
+    fwrite(&pcap_hdr, sizeof(pcap_hdr), 1, file_info->fp);
+    file_info->filesize += sizeof(pcap_hdr);
+
+    // 添加到全局文件列表
+    pthread_mutex_lock(&g_file_list_mutex);
+    file_info->next = g_file_list_head;
+    g_file_list_head = file_info;
+    pthread_mutex_unlock(&g_file_list_mutex);
+
+    return file_info;
+}
+
+// 从全局文件列表中删除文件信息
+static int remove_file_from_list(uint64_t file_num) {
+    pthread_mutex_lock(&g_file_list_mutex);
+
+    arkime_file_info_t *current = g_file_list_head;
+    arkime_file_info_t *prev = NULL;
+
+    while (current) {
+        if (current->num == file_num) {
+            // 找到要删除的文件
+            if (prev) {
+                prev->next = current->next;
+            } else {
+                g_file_list_head = current->next;
+            }
+
+            DPI_LOG(DPI_LOG_DEBUG, "Removed file from list: file_num=%lu, name=%s",
+                    current->num, current->name);
+
+            // 发送文件信息到ES（在删除前）
+            send_file_info_to_es(current);
+
+            free(current);
+            pthread_mutex_unlock(&g_file_list_mutex);
+            return 0;
+        }
+        prev = current;
+        current = current->next;
+    }
+
+    pthread_mutex_unlock(&g_file_list_mutex);
+    DPI_LOG(DPI_LOG_WARNING, "File not found in list: file_num=%lu", file_num);
+    return -1;
+}
+
+// 关闭PCAP文件
+void dpi_arkime_close_pcap_file(arkime_file_info_t *file_info) {
+    if (!file_info) return;
+
+    // 检查ES是否启用
+    if (!g_config.es_config.enabled) {
+        // ES未启用时仍然需要关闭文件，但不发送到ES
+        if (file_info->fp) {
+            fclose(file_info->fp);
+            file_info->fp = NULL;
+        }
+        return;
+    }
+
+    if (file_info->fp) {
+        fclose(file_info->fp);
+        file_info->fp = NULL;
+    }
+
+    // 更新文件大小
+    struct stat st;
+    if (stat(file_info->name, &st) == 0) {
+        file_info->filesize = st.st_size;
+    }
+
+    // 设置为锁定状态
+    file_info->locked = 1;
+
+    DPI_LOG(DPI_LOG_INFO, "Closed PCAP file: %s (size: %lu bytes)",
+            file_info->name, file_info->filesize);
+
+    // 发送文件信息到ES并从列表中删除
+    remove_file_from_list(file_info->num);
+}
+
+// 检查是否需要轮换文件
+int dpi_arkime_need_rotate_file(arkime_file_info_t *file_info, uint32_t packet_len) {
+    if (!file_info) return 1;
+
+    // 检查ES是否启用
+    if (!g_config.es_config.enabled) {
+        // ES未启用时不进行文件轮换
+        return 0;
+    }
+
+    time_t now = time(NULL);
+
+    // 检查文件大小
+    if (file_info->filesize + packet_len + sizeof(struct pcap_pkthdr) > max_file_size_bytes) {
+        return 1;
+    }
+
+    // 检查文件时间
+    if (now - file_info->create_time > max_file_time_seconds) {
+        return 1;
+    }
+
+    return 0;
+}
+
+// 写入包数据到PCAP文件
+int dpi_arkime_write_packet(arkime_file_info_t *file_info,
+                           const uint8_t *packet_data,
+                           uint32_t packet_len,
+                           uint64_t timestamp_usec) {
+    if (!file_info || !file_info->fp || !packet_data) {
+        return -1;
+    }
+
+    // 写入包头
+    struct pcap_pkthdr pkt_hdr;
+    pkt_hdr.ts_sec = timestamp_usec / 1000000;
+    pkt_hdr.ts_usec = timestamp_usec % 1000000;
+    pkt_hdr.caplen = packet_len;
+    pkt_hdr.len = packet_len;
+
+    size_t written = fwrite(&pkt_hdr, sizeof(pkt_hdr), 1, file_info->fp);
+    if (written != 1) {
+        return -1;
+    }
+
+    // 写入包数据
+    written = fwrite(packet_data, packet_len, 1, file_info->fp);
+    if (written != 1) {
+        return -1;
+    }
+
+    // 更新文件信息
+    file_info->filesize += sizeof(pkt_hdr) + packet_len;
+    file_info->packet_count++;
+
+    // 定期刷新文件
+    if (file_info->packet_count % 100 == 0) {
+        fflush(file_info->fp);
+    }
+
+    return 0;
+}
+
+// 获取当前线程的活跃文件，如果不存在或需要轮换则创建新文件
+arkime_file_info_t* dpi_arkime_get_current_file(uint64_t timestamp_usec, uint32_t packet_len) {
+    // 检查当前文件是否需要轮换
+    if (current_file && dpi_arkime_need_rotate_file(current_file, packet_len)) {
+        dpi_arkime_close_pcap_file(current_file);
+        current_file = NULL;
+    }
+
+    // 如果没有当前文件，创建新文件
+    if (!current_file) {
+        current_file = dpi_arkime_create_pcap_file(timestamp_usec / 1000);  // 转换为毫秒
+    }
+
+    return current_file;
+}
+
+// 主要接口：存入pcap包
+int dpi_arkime_store_packet(const uint8_t *packet_data,
+                           uint32_t packet_len,
+                           uint64_t timestamp_usec,
+                           uint64_t *file_num,
+                           uint64_t *file_pos) {
+    if (!packet_data || packet_len == 0) {
+        return -1;
+    }
+
+    // 获取当前文件（如果需要会自动轮换）
+    arkime_file_info_t *file_info = dpi_arkime_get_current_file(timestamp_usec, packet_len);
+    if (!file_info) {
+        return -1;
+    }
+
+    // 记录写入前的文件位置（这就是包的偏移量）
+    uint64_t packet_offset = file_info->filesize;
+
+    // 写入包数据
+    int result = dpi_arkime_write_packet(file_info, packet_data, packet_len, timestamp_usec);
+
+    // 如果写入成功，返回文件信息
+    if (result == 0) {
+        if (file_num) {
+            *file_num = file_info->num;
+        }
+        if (file_pos) {
+            *file_pos = packet_offset;
+        }
+    }
+
+    return result;
+}
+
+// 获取文件信息的JSON格式字符串（用于调试和监控）
+char* dpi_arkime_get_file_info_json(arkime_file_info_t *file_info) {
+    if (!file_info) return NULL;
+
+    static char json_buffer[1024];
+    snprintf(json_buffer, sizeof(json_buffer),
+             "{\n"
+             "  \"num\": %lu,\n"
+             "  \"name\": \"%s\",\n"
+             "  \"first\": %lu,\n"
+             "  \"node\": \"%s\",\n"
+             "  \"filesize\": %lu,\n"
+             "  \"locked\": %d\n"
+             "}",
+             file_info->num,
+             file_info->name,
+             file_info->first,
+             file_info->node,
+             file_info->filesize,
+             file_info->locked);
+
+    return json_buffer;
+}
+
+// 获取当前文件信息
+char* dpi_arkime_get_current_file_info() {
+    if (!current_file) {
+        return NULL;
+    }
+    return dpi_arkime_get_file_info_json(current_file);
+}
+
+// 清理资源
+void dpi_arkime_pcap_cleanup() {
+    // 检查ES是否启用
+    if (!g_config.es_config.enabled) {
+        DPI_LOG(DPI_LOG_DEBUG, "Arkime ES module disabled, skipping pcap cleanup");
+        return;
+    }
+
+    // 关闭当前文件
+    if (current_file) {
+        dpi_arkime_close_pcap_file(current_file);
+        current_file = NULL;
+    }
+
+    // 清理文件列表
+    pthread_mutex_lock(&g_file_list_mutex);
+    arkime_file_info_t *current = g_file_list_head;
+    while (current) {
+        arkime_file_info_t *next = current->next;
+        if (current->fp) {
+            fclose(current->fp);
+        }
+        free(current);
+        current = next;
+    }
+    g_file_list_head = NULL;
+    pthread_mutex_unlock(&g_file_list_mutex);
+}
\ No newline at end of file
diff --git a/src/arkime/dpi_arkime_pcap.h b/src/arkime/dpi_arkime_pcap.h
new file mode 100644
index 00000000..558a0f1c
--- /dev/null
+++ b/src/arkime/dpi_arkime_pcap.h
@@ -0,0 +1,70 @@
+/*
+ * dpi_arkime_pcap.h - Arkime兼容的PCAP文件管理模块头文件
+ */
+
+#ifndef DPI_ARKIME_PCAP_H
+#define DPI_ARKIME_PCAP_H
+
+#include <stdint.h>
+#include <stdio.h>
+
+#ifdef __cplusplus
+extern "C" {
+#endif
+
+// 文件信息结构体前向声明
+typedef struct arkime_file_info arkime_file_info_t;
+
+// iniparser字典类型前向声明
+#ifndef INIPARSER_H
+typedef struct _dictionary_ dictionary;
+#endif
+
+/**
+ * 初始化配置参数
+ * 从配置文件读取相关参数，创建必要的目录
+ * @param ini 配置文件字典指针，如果为NULL则使用默认配置
+ */
+void dpi_arkime_pcap_init_config(dictionary *ini);
+
+/**
+ * 申请一个全局文件序号（线程安全）
+ * @return 全局唯一的文件序号
+ */
+uint64_t dpi_arkime_file_num_creat(void);
+
+/**
+ * 存储数据包到PCAP文件
+ * 这是主要的接口函数，会自动处理文件轮换
+ *
+ * @param packet_data 包数据指针
+ * @param packet_len 包数据长度
+ * @param timestamp_usec 时间戳（微秒）
+ * @param file_num 输出参数，返回文件序号
+ * @param file_pos 输出参数，返回包在文件中的偏移量
+ * @return 0成功，-1失败
+ */
+int dpi_arkime_store_packet(const uint8_t *packet_data,
+                           uint32_t packet_len,
+                           uint64_t timestamp_usec,
+                           uint64_t *file_num,
+                           uint64_t *file_pos);
+
+/**
+ * 获取当前文件信息的JSON格式字符串
+ * @return JSON格式的文件信息字符串，如果没有当前文件返回NULL
+ */
+char* dpi_arkime_get_current_file_info(void);
+
+/**
+ * 清理资源
+ * 关闭所有打开的文件，释放内存
+ */
+void dpi_arkime_pcap_cleanup(void);
+
+
+#ifdef __cplusplus
+}
+#endif
+
+#endif /* DPI_ARKIME_PCAP_H */
diff --git a/src/arkime/dpi_arkime_session.c b/src/arkime/dpi_arkime_session.c
new file mode 100644
index 00000000..e375c1fa
--- /dev/null
+++ b/src/arkime/dpi_arkime_session.c
@@ -0,0 +1,298 @@
+/*打包session的json结构*/
+
+#include <stdio.h>
+#include <stdlib.h>
+#include <string.h>
+#include <arpa/inet.h>
+#include <time.h>
+#include <sys/time.h>
+#include <stdint.h>
+
+#include "dpi_arkime_session.h"
+#include "dpi_arkime_es.h"
+#include "../dpi_log.h"
+#include "../dpi_detect.h"
+#include "../dpi_tbl_log.h"
+#include "../sdtapp_interface.h"
+#include "../../include/cJSON.h"
+
+extern struct global_config g_config;
+
+// 从record创建会话JSON数据
+char* dpi_arkime_create_session_json_from_record(precord_t *record) {
+    // 检查ES是否启用
+    if (!g_config.es_config.enabled) {
+        return NULL; // ES未启用时返回NULL
+    }
+
+    if (!record) {
+        DPI_LOG(DPI_LOG_ERROR, "Invalid record pointer");
+        return NULL;
+    }
+
+    cJSON *session = cJSON_CreateObject();
+    if (!session) {
+        return NULL;
+    }
+
+    // 添加@timestamp字段（当前时间戳，毫秒）
+    struct timeval current_time;
+    gettimeofday(&current_time, NULL);
+    uint64_t timestamp_ms = ((uint64_t)current_time.tv_sec) * 1000 + ((uint64_t)current_time.tv_usec) / 1000;
+    cJSON_AddNumberToObject(session, "@timestamp", timestamp_ms);
+
+    // 创建protocols数组
+    cJSON *protocols_array = cJSON_CreateArray();
+    if (!protocols_array) {
+        cJSON_Delete(session);
+        return NULL;
+    }
+
+    // 创建source和destination对象
+    cJSON *source = cJSON_CreateObject();
+    cJSON *destination = cJSON_CreateObject();
+    cJSON *network = cJSON_CreateObject();
+    if (!source || !destination || !network) {
+        cJSON_Delete(session);
+        cJSON_Delete(protocols_array);
+        if (source) cJSON_Delete(source);
+        if (destination) cJSON_Delete(destination);
+        if (network) cJSON_Delete(network);
+        return NULL;
+    }
+
+    // 初始化统计变量
+    uint64_t src_bytes = 0, dst_bytes = 0, src_packets = 0, dst_packets = 0;
+    uint64_t first_packet = 0, last_packet = 0, session_length = 0;
+    int ip_protocol = 0;
+
+    // 遍历所有层
+    for (player_t *layer = precord_layer_get_first(record); layer != NULL;
+         layer = precord_layer_get_next(record, layer)) {
+
+        const char *layer_name = precord_layer_get_layer_name(layer);
+        precord_layer_move_cursor(record, layer_name);
+
+        // 添加协议到protocols数组
+        cJSON *protocol_name = cJSON_CreateString(layer_name);
+        if (protocol_name) {
+            cJSON_AddItemToArray(protocols_array, protocol_name);
+        }
+
+        // 遍历当前层的所有字段
+        for (pfield_t *field = precord_field_get_first(record);
+            field != NULL;
+            field = precord_field_get_next(record, field)) {
+
+            pfield_desc_t *fdesc = precord_field_get_fdesc(field);
+            ya_fvalue_t *fvalue = precord_field_get_fvalue(field);
+            const char *field_name = pfdesc_get_name(fdesc);
+
+            if (fvalue == NULL) {
+                if (g_config.sdx_out_all_field) {
+                    // 如果配置要求输出所有字段，添加null值
+                    cJSON_AddNullToObject(session, field_name);
+                }
+                continue;
+            }
+
+            char *value_str = ya_fvalue_to_string_repr(fvalue, BASE_NONE);
+            if (!value_str) {
+                continue;
+            }
+
+            // 通用字段处理逻辑 - 所有字段统一处理，映射由Lua适配层完成
+            // 检查是否已存在该协议的对象
+            cJSON *proto_obj = cJSON_GetObjectItem(session, layer_name);
+            if (!proto_obj) {
+                proto_obj = cJSON_CreateObject();
+                if (proto_obj) {
+                    cJSON_AddItemToObject(session, layer_name, proto_obj);
+                }
+            }
+
+            if (proto_obj) {
+                // 智能类型转换：尝试解析为数字，失败则作为字符串
+                char *endptr;
+                long long num_val = strtoll(value_str, &endptr, 10);
+                if (*endptr == '\0') {
+                    // 成功解析为整数
+                    cJSON_AddNumberToObject(proto_obj, field_name, num_val);
+                } else {
+                    // 作为字符串处理
+                    cJSON_AddStringToObject(proto_obj, field_name, value_str);
+                }
+
+                // 如果是general层，还需要收集一些统计信息用于构造必须字段
+                if (strcmp(layer_name, "general") == 0) {
+                    // 收集统计信息，用于后续构造source/destination/network对象
+                    if (strcmp(field_name, "upBytes") == 0) {
+                        src_bytes = strtoull(value_str, NULL, 10);
+                    } else if (strcmp(field_name, "downBytes") == 0) {
+                        dst_bytes = strtoull(value_str, NULL, 10);
+                    } else if (strcmp(field_name, "upPackets") == 0) {
+                        src_packets = strtoull(value_str, NULL, 10);
+                    } else if (strcmp(field_name, "downPackets") == 0) {
+                        dst_packets = strtoull(value_str, NULL, 10);
+                    } else if (strcmp(field_name, "firstPacket") == 0) {
+                        first_packet = strtoull(value_str, NULL, 10);
+                    } else if (strcmp(field_name, "lastPacket") == 0) {
+                        last_packet = strtoull(value_str, NULL, 10);
+                    } else if (strcmp(field_name, "length") == 0) {
+                        session_length = strtoull(value_str, NULL, 10);
+                    } else if (strcmp(field_name, "proto") == 0) {
+                        ip_protocol = atoi(value_str);
+                    }
+                }
+            }
+
+            free(value_str);
+        }
+    }
+
+    // 添加node字段
+    cJSON_AddStringToObject(session, "node", g_config.es_config.node_name);
+
+    // 从general对象中提取信息构造Arkime必须字段
+    cJSON *general_obj = cJSON_GetObjectItem(session, "general");
+    if (general_obj) {
+        // 构造source对象
+        cJSON *src_addr = cJSON_GetObjectItem(general_obj, "srcAddr");
+        cJSON *src_port = cJSON_GetObjectItem(general_obj, "srcPort");
+        cJSON *src_mac = cJSON_GetObjectItem(general_obj, "srcMac");
+
+        if (src_addr && cJSON_IsString(src_addr)) {
+            cJSON_AddStringToObject(source, "ip", cJSON_GetStringValue(src_addr));
+        }
+        if (src_port && cJSON_IsNumber(src_port)) {
+            cJSON_AddNumberToObject(source, "port", cJSON_GetNumberValue(src_port));
+        }
+        if (src_bytes > 0) {
+            cJSON_AddNumberToObject(source, "bytes", src_bytes);
+        }
+        if (src_packets > 0) {
+            cJSON_AddNumberToObject(source, "packets", src_packets);
+        }
+        if (src_mac && cJSON_IsString(src_mac)) {
+            cJSON *mac_array = cJSON_CreateArray();
+            if (mac_array) {
+                cJSON_AddItemToArray(mac_array, cJSON_CreateString(cJSON_GetStringValue(src_mac)));
+                cJSON_AddItemToObject(source, "mac", mac_array);
+            }
+        }
+
+        // 构造destination对象
+        cJSON *dst_addr = cJSON_GetObjectItem(general_obj, "dstAddr");
+        cJSON *dst_port = cJSON_GetObjectItem(general_obj, "dstPort");
+        cJSON *dst_mac = cJSON_GetObjectItem(general_obj, "dstMac");
+
+        if (dst_addr && cJSON_IsString(dst_addr)) {
+            cJSON_AddStringToObject(destination, "ip", cJSON_GetStringValue(dst_addr));
+        }
+        if (dst_port && cJSON_IsNumber(dst_port)) {
+            cJSON_AddNumberToObject(destination, "port", cJSON_GetNumberValue(dst_port));
+        }
+        if (dst_bytes > 0) {
+            cJSON_AddNumberToObject(destination, "bytes", dst_bytes);
+        }
+        if (dst_packets > 0) {
+            cJSON_AddNumberToObject(destination, "packets", dst_packets);
+        }
+        if (dst_mac && cJSON_IsString(dst_mac)) {
+            cJSON *mac_array = cJSON_CreateArray();
+            if (mac_array) {
+                cJSON_AddItemToArray(mac_array, cJSON_CreateString(cJSON_GetStringValue(dst_mac)));
+                cJSON_AddItemToObject(destination, "mac", mac_array);
+            }
+        }
+
+        // 添加session级别的必须字段
+        if (first_packet > 0) {
+            cJSON_AddNumberToObject(session, "firstPacket", first_packet);
+        }
+        if (last_packet > 0) {
+            cJSON_AddNumberToObject(session, "lastPacket", last_packet);
+        }
+        if (session_length > 0) {
+            cJSON_AddNumberToObject(session, "length", session_length);
+        }
+        if (ip_protocol > 0) {
+            cJSON_AddNumberToObject(session, "ipProtocol", ip_protocol);
+        }
+    }
+
+    // 计算并添加network统计
+    uint64_t total_bytes = src_bytes + dst_bytes;
+    uint64_t total_packets = src_packets + dst_packets;
+    cJSON_AddNumberToObject(network, "bytes", total_bytes);
+    cJSON_AddNumberToObject(network, "packets", total_packets);
+
+    // 添加packetPos和fileId数组（从record的文件信息链表中获取）
+    cJSON *packet_pos = cJSON_CreateArray();
+    cJSON *file_id = cJSON_CreateArray();
+    if (packet_pos && file_id) {
+        // 获取record的文件位置数组
+        int64_t *pos_array = NULL;
+        int array_size = 0;
+
+        // 使用函数获取文件位置数组
+        if (arkime_record_get_file_pos_array(record, &pos_array, &array_size) == 0 && pos_array && array_size > 0) {
+            // 添加所有文件位置信息到packetPos数组
+            for (int i = 0; i < array_size; i++) {
+                cJSON_AddItemToArray(packet_pos, cJSON_CreateNumber(pos_array[i]));
+            }
+
+            // 提取文件ID到fileId数组（负值表示文件ID）
+            for (int i = 0; i < array_size; i++) {
+                if (pos_array[i] < 0) {  // 负值表示文件ID
+                    cJSON_AddItemToArray(file_id, cJSON_CreateNumber(-pos_array[i]));
+                }
+            }
+
+            free(pos_array);  // 释放分配的内存
+        } else {
+            // 如果没有文件信息，添加默认值
+            cJSON_AddItemToArray(packet_pos, cJSON_CreateNumber(-1));
+            cJSON_AddItemToArray(packet_pos, cJSON_CreateNumber(0));
+            cJSON_AddItemToArray(file_id, cJSON_CreateNumber(1));
+        }
+    }
+
+    // 将所有对象添加到session
+    cJSON_AddItemToObject(session, "source", source);
+    cJSON_AddItemToObject(session, "destination", destination);
+    cJSON_AddItemToObject(session, "network", network);
+    cJSON_AddItemToObject(session, "protocols", protocols_array);
+    if (packet_pos) cJSON_AddItemToObject(session, "packetPos", packet_pos);
+    if (file_id) cJSON_AddItemToObject(session, "fileId", file_id);
+
+    char *json_string = cJSON_PrintUnformatted(session);
+    cJSON_Delete(session);
+
+    DPI_LOG(DPI_LOG_DEBUG, "Created session JSON from record with file info and general fields");
+
+    return json_string;
+}
+
+// 发送会话数据到ES - 完全基于record
+int dpi_arkime_send_session_to_es(precord_t *record) {
+    if (!record || !g_config.es_config.enabled) {
+        return 0; // ES未启用或无效record时直接返回成功
+    }
+
+    char *session_json = dpi_arkime_create_session_json_from_record(record);
+    if (!session_json) {
+        DPI_LOG(DPI_LOG_ERROR, "Failed to create session JSON from record");
+        return -1;
+    }
+
+    int result = dpi_arkime_es_send_session(session_json);
+    if (result == 0) {
+        DPI_LOG(DPI_LOG_DEBUG, "Sent session data to ES");
+    } else {
+        DPI_LOG(DPI_LOG_WARNING, "Failed to send session data to ES");
+    }
+
+    free(session_json);
+    return result;
+}
\ No newline at end of file
diff --git a/src/arkime/dpi_arkime_session.h b/src/arkime/dpi_arkime_session.h
new file mode 100644
index 00000000..d894e984
--- /dev/null
+++ b/src/arkime/dpi_arkime_session.h
@@ -0,0 +1,43 @@
+/*打包session的json结构*/
+
+#ifndef DPI_ARKIME_SESSION_H
+#define DPI_ARKIME_SESSION_H
+
+#include <stdint.h>
+
+#ifdef __cplusplus
+extern "C" {
+#endif
+
+// 前向声明
+struct flow_info;
+struct ProtoRecord;
+typedef struct ProtoRecord precord_t;
+
+/**
+ * 从flow创建会话JSON数据
+ * @param flow flow信息结构体指针
+ * @return JSON格式的会话数据字符串，需要调用者释放内存
+ */
+char* dpi_arkime_create_session_json_from_flow(struct flow_info *flow);
+
+/**
+ * 从record创建会话JSON数据 - 完全基于record，因为flow可能已失效
+ * 使用record中保存的arkime文件信息和协议字段信息
+ * @param record 协议记录指针（包含flow指针和arkime文件信息）
+ * @return JSON格式的会话数据字符串，需要调用者释放内存
+ */
+char* dpi_arkime_create_session_json_from_record(precord_t *record);
+
+/**
+ * 发送会话数据到ES - 完全基于record
+ * @param record 协议记录指针（包含所有必要信息）
+ * @return 0成功，-1失败
+ */
+int dpi_arkime_send_session_to_es(precord_t *record);
+
+#ifdef __cplusplus
+}
+#endif
+
+#endif /* DPI_ARKIME_SESSION_H */
diff --git a/src/dpi_detect.c b/src/dpi_detect.c
index c4201d9b..92579f7d 100644
--- a/src/dpi_detect.c
+++ b/src/dpi_detect.c
@@ -50,7 +50,7 @@
 #include "dpi_pschema.h"
 #include "dpi_tll.h"
 #include "dpi_numa.h"
-
+#include "arkime/dpi_arkime_pcap.h"
 #include "dpi_dpdk_wrapper.h"
 
 /* mask for Bad FCF presence */
@@ -817,6 +817,9 @@ static struct flow_info* find_or_create_flow(struct work_process_data * workflow
         INIT_LIST_HEAD(&newflow->reassemble_dst2src_head);
         INIT_LIST_HEAD(&newflow->sdt_flow.pkt_stream_head);
 
+        // 初始化arkime文件位置链表
+        newflow->arkime_file_pos_list = NULL;
+
         dpi_flow_timer_init(workflow, newflow);
 
         //将newflow的数据链路层 复制 workflow
@@ -1775,6 +1778,9 @@ int dpi_packet_processing_ip_layer(struct work_process_data *workflow,
 
     flow->pkt=pkt;
 
+    // 添加Arkime文件信息（每个包都添加）
+    arkime_flow_add_file_info(flow, workflow->arkime_file_num, workflow->arkime_file_pos);
+
     // 通联日志在此填充流表信息，解析完毕直接 return
     if (workflow->is_tll == 1) {
         dissect_tll(flow, direction, 0, payload, payload_len, DISSECT_PKT_ORIGINAL);
@@ -2359,6 +2365,21 @@ void do_all_flow_timeout(struct work_process_data *process)
 int workflow_process_packet2 (struct work_process_data * workflow,u_int64_t p_usec, const u_char *raw_packet,
                               uint32_t raw_pkt_len, uint64_t timestamp, void* userdata)
 {
+    // ==================== ARKIME PCAP 文件存储 ====================
+    // 存储原始包数据到PCAP文件，并获取文件信息
+    uint64_t arkime_file_num = 0;
+    uint64_t arkime_file_pos = 0;
+    if (dpi_arkime_store_packet((const uint8_t*)raw_packet, raw_pkt_len, p_usec,
+                               &arkime_file_num, &arkime_file_pos) != 0) {
+        // 如果存储失败，记录错误但继续处理包
+        // 可以在这里添加错误统计
+    }
+
+    // 将文件信息存储到workflow中，后续传递给flow
+    workflow->arkime_file_num = arkime_file_num;
+    workflow->arkime_file_pos = arkime_file_pos;
+    // ==================== ARKIME PCAP 文件存储结束 ====================
+
     struct pkt_tuple_t  tuple;
     struct pkt_info     pkt_data;
     memset(&pkt_data, 0, sizeof(struct pkt_info ));
@@ -2917,3 +2938,281 @@ struct decode_t *decode[]={
     &decode_ssh,
     NULL,
 };
+
+// Arkime文件信息管理函数实现 - 链表版本
+
+// 添加文件信息到flow的链表中
+int arkime_flow_add_file_info(struct flow_info *flow, uint64_t file_num, uint64_t file_pos) {
+    if (!flow) {
+        return -1;
+    }
+
+    // 检查当前链表的最后一个节点
+    struct arkime_file_pos_node *current = flow->arkime_file_pos_list;
+    struct arkime_file_pos_node *last = NULL;
+
+    // 找到链表尾部
+    while (current) {
+        last = current;
+        current = current->next;
+    }
+
+    // 检查是否需要插入文件标识（-1 * file_num）
+    bool need_file_marker = true;
+
+    // 如果链表为空，需要插入文件标识
+    if (!last) {
+        need_file_marker = true;
+    } else {
+        // 从链表尾部向前查找最近的文件标识
+        struct arkime_file_pos_node *search = flow->arkime_file_pos_list;
+        struct arkime_file_pos_node *last_file_marker = NULL;
+
+        while (search) {
+            if (search->value < 0) {
+                last_file_marker = search;  // 记录最后一个文件标识
+            }
+            search = search->next;
+        }
+
+        // 如果找到了文件标识，检查是否是同一个文件
+        if (last_file_marker) {
+            uint64_t last_file_num = (uint64_t)(-last_file_marker->value);
+            if (last_file_num == file_num) {
+                need_file_marker = false;  // 同一个文件，不需要插入文件标识
+            }
+        }
+    }
+
+    // 如果需要，先插入文件标识
+    if (need_file_marker) {
+        struct arkime_file_pos_node *file_node = malloc(sizeof(struct arkime_file_pos_node));
+        if (!file_node) {
+            DPI_LOG(DPI_LOG_ERROR, "Failed to allocate memory for file marker node");
+            return -1;
+        }
+
+        file_node->value = -1 * (int64_t)file_num;  // 负值表示文件号
+        file_node->next = NULL;
+
+        if (last) {
+            last->next = file_node;
+        } else {
+            flow->arkime_file_pos_list = file_node;
+        }
+        last = file_node;
+    }
+
+    // 插入文件位置
+    struct arkime_file_pos_node *pos_node = malloc(sizeof(struct arkime_file_pos_node));
+    if (!pos_node) {
+        DPI_LOG(DPI_LOG_ERROR, "Failed to allocate memory for position node");
+        return -1;
+    }
+
+    pos_node->value = (int64_t)file_pos;  // 正值表示文件偏移量
+    pos_node->next = NULL;
+
+    if (last) {
+        last->next = pos_node;
+    } else {
+        flow->arkime_file_pos_list = pos_node;
+    }
+
+    DPI_LOG(DPI_LOG_DEBUG, "Added arkime file info to flow %lu: file_num=%lu, file_pos=%lu",
+            flow->flow_id, file_num, file_pos);
+
+    return 0;
+}
+
+// 清空flow的文件信息链表
+void arkime_flow_clear_file_info(struct flow_info *flow) {
+    if (!flow) {
+        return;
+    }
+
+    struct arkime_file_pos_node *current = flow->arkime_file_pos_list;
+    while (current) {
+        struct arkime_file_pos_node *next = current->next;
+        free(current);
+        current = next;
+    }
+
+    flow->arkime_file_pos_list = NULL;
+
+    DPI_LOG(DPI_LOG_DEBUG, "Cleared arkime file info for flow %lu", flow->flow_id);
+}
+
+// 获取flow的文件信息数量（统计链表中的文件数）
+int arkime_flow_get_file_count(struct flow_info *flow) {
+    if (!flow) {
+        return 0;
+    }
+
+    int file_count = 0;
+    struct arkime_file_pos_node *current = flow->arkime_file_pos_list;
+
+    while (current) {
+        if (current->value < 0) {  // 负值表示文件标识
+            file_count++;
+        }
+        current = current->next;
+    }
+
+    return file_count;
+}
+
+// 获取flow的文件位置数组（用于JSON构造）
+int arkime_flow_get_file_pos_array(struct flow_info *flow, int64_t **pos_array, int *array_size) {
+    if (!flow || !pos_array || !array_size) {
+        return -1;
+    }
+
+    // 先统计链表长度
+    int count = 0;
+    struct arkime_file_pos_node *current = flow->arkime_file_pos_list;
+    while (current) {
+        count++;
+        current = current->next;
+    }
+
+    if (count == 0) {
+        *pos_array = NULL;
+        *array_size = 0;
+        return 0;
+    }
+
+    // 分配数组内存
+    *pos_array = malloc(count * sizeof(int64_t));
+    if (!*pos_array) {
+        return -1;
+    }
+
+    // 复制链表数据到数组
+    int index = 0;
+    current = flow->arkime_file_pos_list;
+    while (current) {
+        (*pos_array)[index++] = current->value;
+        current = current->next;
+    }
+
+    *array_size = count;
+    return 0;
+}
+
+// ProtoRecord arkime信息管理函数实现 - 链表版本
+
+// 从flow复制文件信息链表到record
+int arkime_record_copy_file_info(ProtoRecord *record, struct flow_info *flow) {
+    if (!record || !flow) {
+        return -1;
+    }
+
+    // 先清空record的现有链表
+    arkime_record_clear_file_info(record);
+
+    // 复制flow的链表
+    struct arkime_file_pos_node *flow_current = flow->arkime_file_pos_list;
+    struct arkime_file_pos_node *record_last = NULL;
+
+    while (flow_current) {
+        struct arkime_file_pos_node *new_node = malloc(sizeof(struct arkime_file_pos_node));
+        if (!new_node) {
+            DPI_LOG(DPI_LOG_ERROR, "Failed to allocate memory for record file info node");
+            arkime_record_clear_file_info(record);  // 清理已分配的节点
+            return -1;
+        }
+
+        new_node->value = flow_current->value;
+        new_node->next = NULL;
+
+        if (record_last) {
+            record_last->next = new_node;
+        } else {
+            record->arkime_file_pos_list = new_node;
+        }
+
+        record_last = new_node;
+        flow_current = flow_current->next;
+    }
+
+    DPI_LOG(DPI_LOG_DEBUG, "Copied arkime file info chain from flow %lu to record",
+            flow->flow_id);
+
+    return 0;
+}
+
+// 清空record的文件信息链表
+void arkime_record_clear_file_info(ProtoRecord *record) {
+    if (!record) {
+        return;
+    }
+
+    struct arkime_file_pos_node *current = record->arkime_file_pos_list;
+    while (current) {
+        struct arkime_file_pos_node *next = current->next;
+        free(current);
+        current = next;
+    }
+
+    record->arkime_file_pos_list = NULL;
+
+    DPI_LOG(DPI_LOG_DEBUG, "Cleared arkime file info for record");
+}
+
+// 获取record的文件信息数量
+int arkime_record_get_file_count(ProtoRecord *record) {
+    if (!record) {
+        return 0;
+    }
+
+    int file_count = 0;
+    struct arkime_file_pos_node *current = record->arkime_file_pos_list;
+
+    while (current) {
+        if (current->value < 0) {  // 负值表示文件标识
+            file_count++;
+        }
+        current = current->next;
+    }
+
+    return file_count;
+}
+
+// 获取record的文件位置数组（用于JSON构造）
+int arkime_record_get_file_pos_array(ProtoRecord *record, int64_t **pos_array, int *array_size) {
+    if (!record || !pos_array || !array_size) {
+        return -1;
+    }
+
+    // 先统计链表长度
+    int count = 0;
+    struct arkime_file_pos_node *current = record->arkime_file_pos_list;
+    while (current) {
+        count++;
+        current = current->next;
+    }
+
+    if (count == 0) {
+        *pos_array = NULL;
+        *array_size = 0;
+        return 0;
+    }
+
+    // 分配数组内存
+    *pos_array = malloc(count * sizeof(int64_t));
+    if (!*pos_array) {
+        return -1;
+    }
+
+    // 复制链表数据到数组
+    int index = 0;
+    current = record->arkime_file_pos_list;
+    while (current) {
+        (*pos_array)[index++] = current->value;
+        current = current->next;
+    }
+
+    *array_size = count;
+    return 0;
+}
diff --git a/src/dpi_detect.h b/src/dpi_detect.h
index 95cbe4ff..f8dfc389 100644
--- a/src/dpi_detect.h
+++ b/src/dpi_detect.h
@@ -712,10 +712,20 @@ struct flow_info {
     // 通联日志相关
     uint8_t         tll_flag;
     DATA_STATE_T    tll_msg;                  // 通联日志消息结构体
+
+    // Arkime文件信息 - 链表结构，支持多文件和多帧
+    // 格式：-1*file_num, file_pos1, file_pos2, ..., -1*next_file_num, file_pos1, ...
+    struct arkime_file_pos_node *arkime_file_pos_list;  // 文件位置链表头
 };
 
 int get_flow_direction(struct flow_info *flow);
 
+// Arkime文件信息管理函数 - 链表版本
+int arkime_flow_add_file_info(struct flow_info *flow, uint64_t file_num, uint64_t file_pos);
+void arkime_flow_clear_file_info(struct flow_info *flow);
+int arkime_flow_get_file_count(struct flow_info *flow);
+int arkime_flow_get_file_pos_array(struct flow_info *flow, int64_t **pos_array, int *array_size);
+
 // flow statistics info
 typedef struct process_stats {
     uint16_t max_packet_len;
@@ -790,6 +800,10 @@ typedef struct work_process_data {
     uint16_t  layers[32];   // 每一帧层级记录
     uint8_t   layer_cnt;    // 每一帧层级计数, 每一帧到来之前置空
     struct p327_header p327_header;//mac作为标签信息解析数据
+
+    // Arkime文件信息 - 当前包的文件信息
+    uint64_t  arkime_file_num;  // 当前包的文件序号
+    uint64_t  arkime_file_pos;  // 当前包在文件中的偏移量
 } dpi_workflow_t;
 
 struct config_http
@@ -1118,6 +1132,17 @@ struct global_config {
     struct sdx_config_variable sdx_config;
     struct web_config_variable web_config;
 
+    // Elasticsearch配置
+    struct {
+        char es_host[256];          // ES服务器地址
+        int es_port;                // ES端口
+        char es_username[64];       // ES用户名
+        char es_password[64];       // ES密码
+        char node_name[64];         // 节点名称
+        int timeout_seconds;        // 超时时间
+        int enabled;                // 是否启用ES发送
+    } es_config;
+
     uint8_t      data_input_with_yn470_line_info;
     uint8_t      data_input_scanning_infinite;
     uint8_t      data_input_scanning_do_rename;
diff --git a/src/dpi_main.c b/src/dpi_main.c
index b26e6b8d..2ee876e6 100644
--- a/src/dpi_main.c
+++ b/src/dpi_main.c
@@ -82,7 +82,9 @@
 #include "dpi_gtp_control.h"
 #include "dpi_numa.h"
 #include "sdx/LineConvert.h"
-
+#include "arkime/dpi_arkime_pcap.h"
+#include "arkime/dpi_arkime_es.h"
+#include "arkime/dpi_arkime_field.h"
 
 
 #ifndef UINT32_BIT
@@ -222,6 +224,17 @@ struct global_config g_config = {
     .pkt_stream_mempool_num     = (1 << 20),
     .tbl_memool_num             = (16384),
     .tbl_ring_size              = (65536),
+
+    // ES配置默认值
+    .es_config = {
+        .es_host = "localhost",
+        .es_port = 9200,
+        .es_username = "",
+        .es_password = "",
+        .node_name = "localhost",
+        .timeout_seconds = 30,
+        .enabled = 0
+    },
 };
 
 struct protocol_record p_record;
@@ -995,7 +1008,9 @@ static void parser_init_config(const char *filename)
 
     g_config.decode_identity_switch  = iniparser_getint(ini, ":DECODE_IDENTITY_SWITCH", 0);
 
-
+    // 解析ES配置
+    dpi_arkime_es_init_config(ini);
+    dpi_arkime_pcap_init_config(ini);
 
 }
 
@@ -2933,7 +2948,9 @@ static void global_destroyer(void)
         MMDB_close(&g_config.mmdb_asn);
     }
 
-
+    // 清理Arkime PCAP模块
+    dpi_arkime_pcap_cleanup();
+    dpi_arkime_es_cleanup();
 
     return;
 }
@@ -3013,6 +3030,9 @@ static void dpi_stop(int sig)
     sdt_clean_rule_data_status();
     tbl_log_file_close_writing();
 
+    // 清理Arkime PCAP模块
+    dpi_arkime_pcap_cleanup();
+
     show_stop_statics_info();
 
     exit(0);
@@ -3408,6 +3428,9 @@ static void init_all_modules(void)
 
     LineConvertInit();  //[SDX] 初始化线路转换库
 
+    // 注册所有字段到Elasticsearch
+    dpi_arkime_register_all_fields();
+
 }
 
 static void init_sdt_module()
diff --git a/src/dpi_sdt_match.c b/src/dpi_sdt_match.c
index 52be8a85..94ad244f 100644
--- a/src/dpi_sdt_match.c
+++ b/src/dpi_sdt_match.c
@@ -555,6 +555,15 @@ sdt_init_prec(const struct pkt_info  *pkt, ProtoRecord *pRec, int dir)
     pRec->pPayload.len=pkt->pkt_len;
     pRec->direction = dir;
     pRec->record = NULL;
+
+    // 初始化arkime文件位置链表
+    pRec->arkime_file_pos_list = NULL;
+
+    // 复制arkime文件信息从flow到record
+    if (pkt->flow) {
+        arkime_record_copy_file_info(pRec, pkt->flow);
+    }
+
     return PKT_OK;
 }
 
diff --git a/src/dpi_tbl_log.c b/src/dpi_tbl_log.c
index ece72bfd..70c4e6a3 100644
--- a/src/dpi_tbl_log.c
+++ b/src/dpi_tbl_log.c
@@ -1914,7 +1914,15 @@ void report_tbl_2_json(struct tbl_log *log, padapt_engine_t * adapt_engine)
         precord_t * adapt_record = NULL;
         ret = dpi_padapt_record_adapt(adapt_engine, &adapt_record, log->record);
         if (adapt_record) {
-            record_write_json(adapt_record, val_elem);
+          record_write_json(adapt_record, val_elem);
+          // 发送session数据到Arkime ES
+          extern int dpi_arkime_send_session_to_es(precord_t *record);
+            int arkime_result = dpi_arkime_send_session_to_es(adapt_record);
+            if (arkime_result != 0) {
+                // 记录发送失败，但不影响正常的日志输出流程
+                printf("WARN: Failed to send session data to Arkime ES for flow_id=%lu\n",
+                       log->record->flow->flow_id);
+            }
             precord_destroy(adapt_record);
         } else {
             record_write_json(log->record, val_elem);
diff --git a/src/sdtapp_interface.h b/src/sdtapp_interface.h
index 20099876..585d75e8 100644
--- a/src/sdtapp_interface.h
+++ b/src/sdtapp_interface.h
@@ -46,7 +46,10 @@ struct value_type
     int             len; //only bytes
     const void     *val;
 };
-
+struct arkime_file_pos_node {
+  int64_t value;  // 负值表示文件号(-1*file_num)，正值表示文件偏移量
+  struct arkime_file_pos_node *next;
+};
 /*
  * 协议解析结果记录类型声明，由 sdt-app 定义
  */
@@ -64,6 +67,10 @@ typedef struct ProtoRecord
     char             proto_name[SDT_PROTO_NAME_SIZE];
     uint16_t         proto_id;
 
+    // Arkime文件信息 - 链表结构，支持多文件和多帧
+    // 格式：-1*file_num, file_pos1, file_pos2, ..., -1*next_file_num, file_pos1, ...
+    struct arkime_file_pos_node *arkime_file_pos_list;  // 文件位置链表头
+
     /* sdt packet payload operation */
     uint8_t          ip_proto;
     uint16_t         l3h_start;       /*二层为基准的网络层head位置偏移量*/
@@ -86,6 +93,12 @@ typedef struct ProtoRecord
     char              val_temp_buff[1024];  //有些需要转换的值,需要使用之后的数据做运算.
 }ProtoRecord;
 
+// ProtoRecord arkime信息管理函数 - 链表版本
+int arkime_record_copy_file_info(ProtoRecord *record, struct flow_info *flow);
+void arkime_record_clear_file_info(ProtoRecord *record);
+int arkime_record_get_file_count(ProtoRecord *record);
+int arkime_record_get_file_pos_array(ProtoRecord *record, int64_t **pos_array, int *array_size);
+
 /* 协议字段头 */
 typedef struct _dpi_field_table_t
 {
-- 
2.43.0

